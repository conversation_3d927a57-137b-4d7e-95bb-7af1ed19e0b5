# AI Assistant Page - Anime Characters Integration

## Overview
AI Assistant page đã được cập nhật để sử dụng các hình nhân vật truyện tranh anime phù hợp với theme của website. Trang này giới thiệu tính năng AI Chatbox với UI hiện đại và các nhân vật anime đáng yêu.

## Required Character Images

Để trang hoạt động tối ưu, b<PERSON><PERSON> c<PERSON>n thêm các hình ảnh anime characters sau vào thư mục `public/`:

### Main Characters
- `ai-assistant-avatar.png` - <PERSON><PERSON> ch<PERSON><PERSON> của AI Assistant (128x128px)
- `default-anime-avatar.png` - Avatar mặc định khi không load được hình (64x64px)

### Supporting Characters (Hero Section)
- `manga-char-1.png` - N<PERSON><PERSON> vật manga 1 (64x64px)
- `manga-char-2.png` - <PERSON><PERSON><PERSON> vật manga 2 (64x64px) 
- `manga-char-3.png` - <PERSON><PERSON><PERSON> vật manga 3 (64x64px)

### Feature Characters
- `search-character.png` - <PERSON><PERSON><PERSON> vật cho tính năng tìm kiếm (48x48px)
- `recommend-character.png` - Nhân vật cho tính năng gợi ý (48x48px)
- `discussion-character.png` - Nhân vật cho tính năng thảo luận (48x48px)
- `assistant-character.png` - Nhân vật cho tính năng trợ lý (48x48px)

### Question Characters
- `question-char-1.png` - Nhân vật câu hỏi 1 (48x48px)
- `question-char-2.png` - Nhân vật câu hỏi 2 (48x48px)
- `question-char-3.png` - Nhân vật câu hỏi 3 (48x48px)
- `question-char-4.png` - Nhân vật câu hỏi 4 (48x48px)

### CTA Characters
- `cta-character-left.png` - Nhân vật CTA bên trái (128x auto)
- `cta-character-right.png` - Nhân vật CTA bên phải (128x auto)

## Character Design Guidelines

### Style Requirements
- **Art Style**: Anime/manga style phù hợp với theme truyện tranh
- **Color Palette**: Bright, colorful, friendly colors
- **Expression**: Friendly, welcoming, enthusiastic expressions
- **Background**: Transparent PNG hoặc solid color background

### Character Types Suggestions
- **AI Assistant**: Robot/cyborg anime character hoặc magical girl
- **Search Character**: Detective/explorer anime character
- **Recommend Character**: Librarian/scholar anime character  
- **Discussion Character**: Social/talkative anime character
- **Assistant Character**: Helper/support anime character
- **Question Characters**: Curious/inquisitive anime characters
- **CTA Characters**: Energetic/motivational anime characters

### Technical Specifications
- **Format**: PNG với transparent background
- **Quality**: High resolution, crisp details
- **Optimization**: Optimized for web (< 50KB per image)
- **Responsive**: Looks good at different sizes

## Fallback System

Component đã được thiết kế với fallback system:
- Nếu hình ảnh không load được, sẽ fallback về `default-anime-avatar.png`
- Nếu default image cũng không có, browser sẽ hiển thị alt text
- Error handling được implement để tránh broken images

## Animation Features

### CSS Animations
- **Float Animation**: Characters float up and down gently
- **Hover Effects**: Characters react when user hovers
- **Entrance Animations**: Characters appear with smooth transitions
- **Responsive Animations**: Animations adapt to screen size

### Performance Optimizations
- **Lazy Loading**: Non-critical images load lazily
- **Eager Loading**: Main AI assistant avatar loads immediately
- **Image Optimization**: Proper sizing and compression
- **Animation Performance**: Hardware-accelerated CSS animations

## Usage Examples

```typescript
// Get character image with fallback
getCharacterImage('aiAssistant') // Returns '/ai-assistant-avatar.png'

// Handle image errors
onImageError(event) // Automatically sets fallback image
```

```html
<!-- Character with error handling -->
<img [src]="getCharacterImage('aiAssistant')" 
     alt="AI Assistant" 
     class="character-image"
     (error)="onImageError($event)">
```

## Customization

### Adding New Characters
1. Add image path to `characterImages` object in component
2. Add corresponding method call in template
3. Update this README with new character info

### Changing Character Styles
- Modify CSS classes in `ai-assistant.component.scss`
- Update animation keyframes for different effects
- Adjust responsive breakpoints as needed

## Browser Support
- Modern browsers with CSS Grid and Flexbox support
- CSS animations and transforms
- PNG transparency support
- Responsive image loading

## Notes
- All images should be optimized for web performance
- Consider using WebP format for better compression
- Test on different screen sizes and devices
- Ensure accessibility with proper alt text
