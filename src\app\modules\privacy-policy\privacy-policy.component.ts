import { Component, OnInit } from '@angular/core';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
    selector: 'app-privacy-policy',
    templateUrl: './privacy-policy.component.html',
    styleUrl: './privacy-policy.component.scss',
    standalone: false
})
export class PrivacyPolicyComponent implements OnInit {
    host = '';
    siteName = 'MeTruyenMoi';
    currentDate = '';

    constructor(
        private urlService: UrlService,
        private seoService: SeoService
    ) {
        this.host = this.urlService.baseUrl;
        this.currentDate = new Date().toLocaleDateString('vi-VN');
    }

    ngOnInit(): void {
        this.setupSEO();
    }

    private setupSEO(): void {
        this.seoService.setTitle('Chính sách bảo mật - <PERSON><PERSON><PERSON> vệ thông tin cá nhân');
        this.seoService.addTags([
            { name: 'description', content: '<PERSON><PERSON>h sách bảo mật chi tiết của MeTruyenMoi - <PERSON> kết bảo vệ thông tin cá nhân và quyền riêng tư của người dùng khi sử dụng dịch vụ đọc truyện tranh trực tuyến.' },
            { name: 'robots', content: 'index, follow' },
            { name: 'author', content: 'MeTruyenMoi' },
            { property: 'og:description', content: 'Chính sách bảo mật chi tiết - Cam kết bảo vệ thông tin cá nhân và quyền riêng tư của người dùng MeTruyenMoi' },
            { property: 'og:title', content: 'Chính sách bảo mật - MeTruyenMoi' },
            { property: 'og:url', content: `${this.host}/chinh-sach-bao-mat` },
            { property: 'og:type', content: 'website' },
            { property: 'og:site_name', content: 'MeTruyenMoi' },
            { property: 'og:locale', content: 'vi_VN' },
            { itemprop: 'name', content: 'Chính sách bảo mật' },
            { itemprop: 'description', content: 'Chính sách bảo mật chi tiết của MeTruyenMoi' },
            { name: 'twitter:card', content: 'summary' },
            { name: 'twitter:title', content: 'Chính sách bảo mật - MeTruyenMoi' },
            { name: 'twitter:description', content: 'Chính sách bảo mật chi tiết - Cam kết bảo vệ thông tin cá nhân và quyền riêng tư' }
        ]);
        this.seoService.updateLink('canonical', `${this.host}/chinh-sach-bao-mat`);
    }

    // Scroll to section method for table of contents
    scrollToSection(sectionId: string): void {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
}
