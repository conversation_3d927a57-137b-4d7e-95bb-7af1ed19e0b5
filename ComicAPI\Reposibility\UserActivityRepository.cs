using ComicAPI.Data;
using ComicAPI.DTOs;
using ComicAPI.Enums;
using ComicAPI.Models.Activity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Concurrent;

namespace ComicAPI.Reposibility
{
    public class UserActivityRepository : IUserActivityRepository
    {
        private readonly ComicDbContext _dbContext;
        private readonly IMemoryCache _cache;
        private readonly ILogger<UserActivityRepository> _logger;
        
        private const string CACHE_PREFIX = "user_activity_";
        private const int DEFAULT_CACHE_MINUTES = 10;
        private const int BATCH_SIZE = 1000;

        public UserActivityRepository(
            ComicDbContext dbContext,
            IMemoryCache cache,
            ILogger<UserActivityRepository> logger)
        {
            _dbContext = dbContext;
            _cache = cache;
            _logger = logger;
        }

        #region Activity CRUD Operations

        public async Task<int> CreateActivityAsync(UserActivity activity)
        {
            try
            {
                _dbContext.UserActivities.Add(activity);
                await _dbContext.SaveChangesAsync();
                return activity.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating activity for user {UserId}", activity.UserId);
                return 0;
            }
        }

        public async Task<List<int>> CreateActivitiesBatchAsync(IEnumerable<UserActivity> activities)
        {
            try
            {
                var activityList = activities.ToList();
                _dbContext.UserActivities.AddRange(activityList);
                await _dbContext.SaveChangesAsync();
                return activityList.Select(a => a.Id).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating batch activities");
                return new List<int>();
            }
        }

        public async Task<UserActivity?> GetActivityByIdAsync(int id)
        {
            var cacheKey = $"{CACHE_PREFIX}activity_{id}";
            
            if (_cache.TryGetValue(cacheKey, out UserActivity? cachedActivity))
                return cachedActivity;

            var activity = await _dbContext.UserActivities
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == id);

            if (activity != null)
            {
                _cache.Set(cacheKey, activity, TimeSpan.FromMinutes(DEFAULT_CACHE_MINUTES));
            }

            return activity;
        }

        public async Task<List<UserActivity>> GetUserActivitiesAsync(int userId, int page = 1, int pageSize = 20, 
            UserActivityType? activityType = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var cacheKey = $"{CACHE_PREFIX}user_activities_{userId}_{page}_{pageSize}_{activityType}_{fromDate?.Ticks}_{toDate?.Ticks}";
            
            if (_cache.TryGetValue(cacheKey, out List<UserActivity>? cachedActivities))
                return cachedActivities!;

            var query = _dbContext.UserActivities
                .AsNoTracking()
                .Where(a => a.UserId == userId);

            if (activityType.HasValue)
                query = query.Where(a => a.ActivityType == activityType.Value);

            if (fromDate.HasValue)
                query = query.Where(a => a.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(a => a.CreatedAt <= toDate.Value);

            var activities = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            _cache.Set(cacheKey, activities, TimeSpan.FromMinutes(5)); // Shorter cache for activity lists
            return activities;
        }

        public async Task<int> GetUserActivitiesCountAsync(int userId, UserActivityType? activityType = null, 
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbContext.UserActivities
                .Where(a => a.UserId == userId);

            if (activityType.HasValue)
                query = query.Where(a => a.ActivityType == activityType.Value);

            if (fromDate.HasValue)
                query = query.Where(a => a.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(a => a.CreatedAt <= toDate.Value);

            return await query.CountAsync();
        }

        #endregion

        #region Reading History CRUD Operations

        public async Task<int> CreateReadingHistoryAsync(UserReadingHistory history)
        {
            try
            {
                _dbContext.UserReadingHistories.Add(history);
                await _dbContext.SaveChangesAsync();
                
                // Invalidate related cache
                await InvalidateUserCacheAsync(history.UserId);
                
                return history.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating reading history for user {UserId}", history.UserId);
                return 0;
            }
        }

        public async Task<List<int>> CreateReadingHistoriesBatchAsync(IEnumerable<UserReadingHistory> histories)
        {
            try
            {
                var historyList = histories.ToList();
                _dbContext.UserReadingHistories.AddRange(historyList);
                await _dbContext.SaveChangesAsync();
                
                // Invalidate cache for affected users
                var userIds = historyList.Select(h => h.UserId).Distinct();
                foreach (var userId in userIds)
                {
                    await InvalidateUserCacheAsync(userId);
                }
                
                return historyList.Select(h => h.Id).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating batch reading histories");
                return new List<int>();
            }
        }

        public async Task<UserReadingHistory?> GetReadingHistoryAsync(int userId, int comicId, int chapterId)
        {
            var cacheKey = $"{CACHE_PREFIX}reading_history_{userId}_{comicId}_{chapterId}";
            
            if (_cache.TryGetValue(cacheKey, out UserReadingHistory? cachedHistory))
                return cachedHistory;

            var history = await _dbContext.UserReadingHistories
                .AsNoTracking()
                .FirstOrDefaultAsync(h => h.UserId == userId && h.ComicId == comicId && h.ChapterId == chapterId);

            if (history != null)
            {
                _cache.Set(cacheKey, history, TimeSpan.FromMinutes(DEFAULT_CACHE_MINUTES));
            }

            return history;
        }

        public async Task<bool> UpdateReadingHistoryAsync(UserReadingHistory history)
        {
            try
            {
                _dbContext.UserReadingHistories.Update(history);
                await _dbContext.SaveChangesAsync();
                
                // Update cache
                var cacheKey = $"{CACHE_PREFIX}reading_history_{history.UserId}_{history.ComicId}_{history.ChapterId}";
                _cache.Set(cacheKey, history, TimeSpan.FromMinutes(DEFAULT_CACHE_MINUTES));
                
                // Invalidate user cache
                await InvalidateUserCacheAsync(history.UserId);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating reading history {Id}", history.Id);
                return false;
            }
        }

        public async Task<List<UserReadingHistory>> GetUserReadingHistoryAsync(int userId, int page = 1, int pageSize = 20, 
            int? comicId = null, bool? isCompleted = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbContext.UserReadingHistories
                .AsNoTracking()
                .Include(h => h.Comic)
                .Include(h => h.Chapter)
                .Where(h => h.UserId == userId);

            if (comicId.HasValue)
                query = query.Where(h => h.ComicId == comicId.Value);

            if (isCompleted.HasValue)
                query = query.Where(h => h.IsCompleted == isCompleted.Value);

            if (fromDate.HasValue)
                query = query.Where(h => h.LastReadAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(h => h.LastReadAt <= toDate.Value);

            return await query
                .OrderByDescending(h => h.LastReadAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetUserReadingHistoryCountAsync(int userId, int? comicId = null, bool? isCompleted = null)
        {
            var query = _dbContext.UserReadingHistories
                .Where(h => h.UserId == userId);

            if (comicId.HasValue)
                query = query.Where(h => h.ComicId == comicId.Value);

            if (isCompleted.HasValue)
                query = query.Where(h => h.IsCompleted == isCompleted.Value);

            return await query.CountAsync();
        }

        #endregion

        #region Login History CRUD Operations

        public async Task<int> CreateLoginHistoryAsync(UserLoginHistory loginHistory)
        {
            try
            {
                _dbContext.UserLoginHistories.Add(loginHistory);
                await _dbContext.SaveChangesAsync();
                return loginHistory.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating login history for user {UserId}", loginHistory.UserId);
                return 0;
            }
        }

        public async Task<List<UserLoginHistory>> GetUserLoginHistoryAsync(int userId, int page = 1, int pageSize = 20, 
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbContext.UserLoginHistories
                .AsNoTracking()
                .Where(h => h.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(h => h.LoginAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(h => h.LoginAt <= toDate.Value);

            return await query
                .OrderByDescending(h => h.LoginAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<bool> UpdateLoginHistoryLogoutAsync(int loginHistoryId, DateTime logoutTime, int sessionDurationSeconds)
        {
            try
            {
                var loginHistory = await _dbContext.UserLoginHistories.FindAsync(loginHistoryId);
                if (loginHistory != null)
                {
                    loginHistory.LogoutAt = logoutTime;
                    loginHistory.SessionDurationSeconds = sessionDurationSeconds;
                    await _dbContext.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating login history logout {Id}", loginHistoryId);
                return false;
            }
        }

        #endregion

        #region Session Activity CRUD Operations

        public async Task<int> CreateSessionActivityAsync(UserSessionActivity sessionActivity)
        {
            try
            {
                _dbContext.UserSessionActivities.Add(sessionActivity);
                await _dbContext.SaveChangesAsync();
                
                // Cache the session
                var cacheKey = $"{CACHE_PREFIX}session_{sessionActivity.SessionId}";
                _cache.Set(cacheKey, sessionActivity, TimeSpan.FromHours(1));
                
                return sessionActivity.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating session activity for user {UserId}", sessionActivity.UserId);
                return 0;
            }
        }

        public async Task<UserSessionActivity?> GetActiveSessionAsync(int userId, string sessionId)
        {
            var cacheKey = $"{CACHE_PREFIX}session_{sessionId}";
            
            if (_cache.TryGetValue(cacheKey, out UserSessionActivity? cachedSession))
                return cachedSession;

            var session = await _dbContext.UserSessionActivities
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.UserId == userId && s.SessionId == sessionId && s.IsActive);

            if (session != null)
            {
                _cache.Set(cacheKey, session, TimeSpan.FromHours(1));
            }

            return session;
        }

        public async Task<bool> UpdateSessionActivityAsync(UserSessionActivity sessionActivity)
        {
            try
            {
                _dbContext.UserSessionActivities.Update(sessionActivity);
                await _dbContext.SaveChangesAsync();
                
                // Update cache
                var cacheKey = $"{CACHE_PREFIX}session_{sessionActivity.SessionId}";
                _cache.Set(cacheKey, sessionActivity, TimeSpan.FromHours(1));
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating session activity {Id}", sessionActivity.Id);
                return false;
            }
        }

        public async Task<bool> EndSessionAsync(string sessionId, DateTime endTime)
        {
            try
            {
                var session = await _dbContext.UserSessionActivities
                    .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.IsActive);

                if (session != null)
                {
                    session.EndedAt = endTime;
                    session.IsActive = false;
                    session.TotalDurationSeconds = (int)(endTime - session.StartedAt).TotalSeconds;
                    
                    await _dbContext.SaveChangesAsync();
                    
                    // Remove from cache
                    var cacheKey = $"{CACHE_PREFIX}session_{sessionId}";
                    _cache.Remove(cacheKey);
                    
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ending session {SessionId}", sessionId);
                return false;
            }
        }

        public async Task<List<UserSessionActivity>> GetUserActiveSessionsAsync(int userId)
        {
            return await _dbContext.UserSessionActivities
                .AsNoTracking()
                .Where(s => s.UserId == userId && s.IsActive)
                .OrderByDescending(s => s.LastActivityAt)
                .ToListAsync();
        }

        public async Task<int> GetUserActiveSessionsCountAsync(int userId)
        {
            return await _dbContext.UserSessionActivities
                .CountAsync(s => s.UserId == userId && s.IsActive);
        }

        #endregion

        // Additional methods will be implemented in the next part...
        public Task<UserActivityStatsDTO> GetUserActivityStatsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            throw new NotImplementedException();
        }

        public Task<List<DailyActivityDTO>> GetDailyActivityAsync(int userId, DateTime fromDate, DateTime toDate)
        {
            throw new NotImplementedException();
        }

        public Task<List<MonthlyActivityDTO>> GetMonthlyActivityAsync(int userId, int year)
        {
            throw new NotImplementedException();
        }

        public Task<List<GenreStatsDTO>> GetUserGenreStatsAsync(int userId)
        {
            throw new NotImplementedException();
        }

        public Task<Dictionary<UserActivityType, int>> GetActivityTypeCountsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            throw new NotImplementedException();
        }

        public Task<DateTime?> GetUserLastActivityAsync(int userId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> UpdateUserLastActivityAsync(int userId, DateTime lastActivity)
        {
            throw new NotImplementedException();
        }

        public Task<bool> IsUserActiveAsync(int userId, TimeSpan timeWindow)
        {
            throw new NotImplementedException();
        }

        public Task<List<int>> GetActiveUsersAsync(TimeSpan timeWindow, int limit = 100)
        {
            throw new NotImplementedException();
        }

        public Task<Dictionary<int, DateTime>> GetUsersLastActivityBatchAsync(IEnumerable<int> userIds)
        {
            throw new NotImplementedException();
        }

        public Task<int> CleanupOldActivitiesAsync(DateTime cutoffDate)
        {
            throw new NotImplementedException();
        }

        public Task<int> CleanupOldLoginHistoriesAsync(DateTime cutoffDate)
        {
            throw new NotImplementedException();
        }

        public Task<int> CleanupInactiveSessionsAsync(DateTime cutoffDate)
        {
            throw new NotImplementedException();
        }

        public Task<bool> ArchiveOldDataAsync(DateTime cutoffDate)
        {
            throw new NotImplementedException();
        }

        public Task<bool> BulkInsertActivitiesAsync(IEnumerable<UserActivity> activities)
        {
            throw new NotImplementedException();
        }

        public Task<bool> BulkInsertReadingHistoriesAsync(IEnumerable<UserReadingHistory> histories)
        {
            throw new NotImplementedException();
        }

        public Task<bool> BulkUpdateReadingProgressAsync(IEnumerable<(int userId, int comicId, int chapterId, float progress, int readingTime)> updates)
        {
            throw new NotImplementedException();
        }

        public async Task InvalidateUserCacheAsync(int userId)
        {
            var keysToRemove = new List<string>
            {
                $"{CACHE_PREFIX}last_activity_{userId}",
                $"{CACHE_PREFIX}stats_{userId}",
                $"{CACHE_PREFIX}genre_stats_{userId}"
            };

            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }
        }

        public Task<T?> GetFromCacheAsync<T>(string key) where T : class
        {
            _cache.TryGetValue(key, out T? value);
            return Task.FromResult(value);
        }

        public Task SetCacheAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            var options = new MemoryCacheEntryOptions();
            if (expiration.HasValue)
                options.SetAbsoluteExpiration(expiration.Value);
            else
                options.SetAbsoluteExpiration(TimeSpan.FromMinutes(DEFAULT_CACHE_MINUTES));

            _cache.Set(key, value, options);
            return Task.CompletedTask;
        }

        public Task RemoveFromCacheAsync(string key)
        {
            _cache.Remove(key);
            return Task.CompletedTask;
        }

        public Task<List<UserActivity>> GetRecentActivitiesByTypeAsync(UserActivityType activityType, int limit = 100, DateTime? since = null)
        {
            throw new NotImplementedException();
        }

        public Task<Dictionary<int, int>> GetTopActiveUsersAsync(DateTime fromDate, DateTime toDate, int limit = 50)
        {
            throw new NotImplementedException();
        }

        public Task<List<(int ComicId, int ReadCount)>> GetMostReadComicsAsync(int userId, int limit = 10)
        {
            throw new NotImplementedException();
        }

        public Task<List<(int ChapterId, DateTime LastRead)>> GetRecentlyReadChaptersAsync(int userId, int limit = 20)
        {
            throw new NotImplementedException();
        }

        public Task<Dictionary<DateTime, int>> GetUserActivityHeatmapAsync(int userId, DateTime fromDate, DateTime toDate)
        {
            throw new NotImplementedException();
        }
    }
}
