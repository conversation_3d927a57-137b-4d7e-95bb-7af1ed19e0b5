import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  signal,
  ViewEncapsulation
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { Comic } from '@schema';
import { UrlService } from '@services/url.service';
import { Subscription, timer } from 'rxjs';

@Component({
  selector: 'div[app-carousel-layout]',
  templateUrl: './carousel-layout.component.html',
  styleUrl: './carousel-layout.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, RouterLink]
})
export class CarouselLayoutComponent implements OnDestroy {

  // Static configuration using readonly for better performance
  readonly classes = [
    "grid-item col-span-1 row-span-2 col-start-1 row-start-1",
    "grid-item col-span-1 row-span-2 col-start-1 row-start-3 w-full",
    "grid-item col-span-2 col-start-2 row-span-full",
    "grid-item col-span-1 row-span-2 col-start-4 row-start-1",
    "grid-item col-span-1 row-span-2 col-start-4 row-start-3",
  ];

  // State using signals
  images = signal<string[]>([]);
  timer?: Subscription;

  @Input() comics?: Comic[] = [];
  @Output() comicHover = new EventEmitter<Comic>();

  // Computed properties
  hasComics = computed(() => this.comics && this.comics.length > 0);

  // TrackBy function for ngFor optimization
  trackByComicId = (index: number, comic: Comic): number => comic.id;

  constructor(private urlService: UrlService) {
    // Initialize images if needed

  }
  getComicUrl(comic: Comic): any {
    return this.urlService.getComicDetailUrl(comic);
  }
  ngOnDestroy(): void {
    this.timer?.unsubscribe();
  }

  // Optimized hover handling with debounce
  OnComicHover(comic: Comic): void {
    this.timer?.unsubscribe();
    this.timer = timer(750).subscribe(() => {
      this.comicHover.emit(comic);
    });
  }

  OnComicLeave(): void {
    this.comicHover.emit(undefined);
    this.timer?.unsubscribe();
  }
}
