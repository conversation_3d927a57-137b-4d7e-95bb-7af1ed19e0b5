import {
  isPlatformServer
} from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicList, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
@Component({
  selector: '[app-home]',
  templateUrl: './home-page.component.html',
  styleUrl: './home-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomePageComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  listComics: Comic[] = [];
  totalpage!: number;
  currentPage = 1;

  // TrackBy function for performance optimization
  trackByComicId = (index: number, comic: Comic): number => {
    return comic?.id ?? index;
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private seoService: SeoService,
    private urlService: UrlService,
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
  ) {
    super(cdr, platformId);
    this.initializeSEO();
  }

  private initializeSEO(): void {
    const seoData = {
      title: 'Đọc Truyện Tranh Online Miễn Phí - Manga, Manhwa, Manhua Hay Nhất',
      description: 'Website đọc truyện tranh online hàng đầu Việt Nam với hơn 10,000+ bộ manga, manhwa, manhua. Cập nhật nhanh nhất, chất lượng HD, hoàn toàn miễn phí. Đọc ngay!',
      type: 'website' as const,
      url: this.urlService.baseUrl,
      image: `${this.urlService.baseUrl}/logo.png`,
      siteName: 'MeTruyenMoi',
      locale: 'vi_VN',
      twitterCard: 'summary_large_image' as const
    };

    this.seoService.setSEOData(seoData);

    // Add website and organization structured data
    const websiteSchema = this.seoService.generateWebsiteSchema();
    const organizationSchema = this.seoService.generateOrganizationSchema();

    // Add breadcrumb for homepage
    const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl }
    ]);

    // Combine schemas
    this.seoService.addStructuredData([websiteSchema, organizationSchema, breadcrumbSchema]);
  }

  ngOnInit(): void {
    this.addSubscription(
      this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
        const page = Number(params['page']) || 1;
        this.currentPage = page;
        this.refreshPage(page);
      })
    );
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }


  refreshPage(page: number, size = 30): void {
    this.listComics = [];
    this.addSubscription(
      this.comicService.getComics({
        step: size.toString(),
        genre: '-1',
        page: page.toString(),
        sort: '1',
        status: '-1',
      })
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        this.totalpage = res.data!.totalpage;
        this.listComics = res.data!.comics;

        if (this.ssr()) {
          this.listComics = this.listComics.slice(0, 10);
        }

        // Update SEO for pagination
        if (page > 1) {
          this.updatePaginationSEO(page);
        }

        // Add ItemList structured data for comics
        this.addComicsStructuredData();

        this.safeMarkForCheck();
      })
    );
  }

  private updatePaginationSEO(page: number): void {
    const title = `Trang ${page} - Truyện Tranh Mới Nhất | MeTruyenMoi`;
    const description = `Xem trang ${page} của danh sách truyện tranh mới nhất được cập nhật liên tục. Hơn 50,000+ bộ manga, manhwa, manhua chất lượng cao.`;

    this.seoService.setSEOData({
      title,
      description,
      url: `${this.urlService.baseUrl}?page=${page}`,
      type: 'website',
      canonical: `${this.urlService.baseUrl}?page=${page}`
    });
  }

  private addComicsStructuredData(): void {
    if (this.listComics.length > 0) {
      // Use the enhanced comic list schema from SEO service
      const itemListSchema = this.seoService.generateComicListSchema(
        this.listComics.slice(0, 20), // Show top 20 for better performance
        'Truyện Tranh Mới Nhất',
        'Danh sách truyện tranh mới cập nhật hàng ngày tại MeTruyenMoi'
      );

      // Get existing schemas
      const websiteSchema = this.seoService.generateWebsiteSchema();
      const organizationSchema = this.seoService.generateOrganizationSchema();
      const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url:   this.urlService.baseUrl }
      ]);

      // Combine all schemas
      const combinedSchemas = [websiteSchema, organizationSchema, breadcrumbSchema, itemListSchema];
      this.seoService.addStructuredData(combinedSchemas);
    }
  }
  onChangePage(page: number) {
    this.router.navigate([''], {
      queryParams: { page: page },
      fragment: 'comics',
    });
  }
  ssr() {
    return isPlatformServer(this.platformId);
  }
}
