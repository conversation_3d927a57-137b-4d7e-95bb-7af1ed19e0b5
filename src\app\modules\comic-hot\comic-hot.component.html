<app-breadcrumb
  class="z-10 my-2 md:container mx-auto flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Truyện tranh hot', url: '' }
  ]"
>
</app-breadcrumb>
<div class="md:container mx-auto">
  <div class="grid grid-cols-1 xl:grid-cols-4 gap-4">
    <div
      id="listComic"
      class="col-span-1 xl:col-span-3 row-start-1 xl:row-start-1"
    >
      <app-grid-comic
        [listComics]="listComics || []"
        [nPreview]="30"
        [title]="'Truyện tranh hot'"
      >
        <ng-template #iconTemplate>
          <svg
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
          >
            <path
              d="M22 7L14.6203 14.3347C13.6227 15.3263 13.1238 15.822 12.5051 15.822C11.8864 15.8219 11.3876 15.326 10.3902 14.3342L10.1509 14.0962C9.15254 13.1035 8.65338 12.6071 8.03422 12.6074C7.41506 12.6076 6.91626 13.1043 5.91867 14.0977L2 18"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              d="M22.0001 12.5458V7H16.418"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
        </ng-template>
      </app-grid-comic>
      <app-pagination
        [totalpage]="totalpage"
        [currentPage]="page"
        (OnChange)="onChangePage($event)"
      >
      </app-pagination>
    </div>
    <div class="col-span-1">
      <app-top-list class="my-4 block"></app-top-list>
    </div>
  </div>
</div>
