import { Component, OnInit, signal, computed, PLATFORM_ID, Inject, ChangeDetectorRef } from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';

@Component({
  selector: 'app-ai-assistant',
  templateUrl: './ai-assistant.component.html',
  styleUrls: ['./ai-assistant.component.scss'],
  standalone: false
})
export class AiAssistantComponent extends OptimizedBaseComponent implements OnInit {
  
  // Signals for reactive state management
  private readonly isLoadingSignal = signal<boolean>(false);
  private readonly chatBoxVisibleSignal = signal<boolean>(false);
  
  // Computed properties
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly chatBoxVisible = computed(() => this.chatBoxVisibleSignal());

  constructor(
    private seoService: SeoService,
    private urlService: UrlService,
    override  cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.setupSEO();
  }

  /**
   * Open AI Chatbox
   */
  openChatBox(): void {
    this.runInBrowser(() => {
      // Show the chat box bubble component
      if (ChatBoxBubbleComponent.Instance) {
        // Hide the bubble and show the chat box
        ChatBoxBubbleComponent.Instance.showChat();
        this.chatBoxVisibleSignal.set(true);
      } else {
        // If no instance available, show a message
        console.warn('ChatBox instance not available. Please ensure the chat-box-bubble component is loaded.');
      }
    });
  }

  /**
   * Setup SEO for AI Assistant page
   */
  private setupSEO(): void {
    const title = 'Trợ lý AI - Chatbot thông minh cho truyện tranh | MeTruyenMoi';
    const description = 'Trợ lý AI thông minh giúp bạn tìm kiếm, gợi ý và trò chuyện về truyện tranh. Khám phá tính năng chatbot AI tiên tiến tại MeTruyenMoi.';
    const url = `${this.urlService.baseUrl}/tro-ly-ai`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      url,
      image: '/logo.png',
      siteName: 'MeTruyenMoi',
      canonical: url,
      twitterCard: 'summary_large_image' as const,
      keywords: 'trợ lý AI, chatbot, truyện tranh, AI assistant, comic chatbot, manga AI'
    };

    this.seoService.setSEOData(seoData);

    // Add structured data for AI Assistant page
    const pageSchema = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: 'Trợ lý AI',
      description: description,
      url: url,
      mainEntity: {
        '@type': 'SoftwareApplication',
        name: 'Trợ lý AI MeTruyenMoi',
        description: 'Chatbot AI thông minh cho truyện tranh',
        applicationCategory: 'ChatApplication',
        operatingSystem: 'Web Browser'
      }
    };

    this.seoService.addStructuredData([pageSchema]);
  }


}
