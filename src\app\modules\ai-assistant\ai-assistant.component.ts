import { Component, OnInit, signal, computed, PLATFORM_ID, Inject, ChangeDetectorRef } from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';

@Component({
  selector: 'app-ai-assistant',
  templateUrl: './ai-assistant.component.html',
  styleUrls: ['./ai-assistant.component.scss'],
  standalone: false
})
export class AiAssistantComponent extends OptimizedBaseComponent implements OnInit {

  // Signals for reactive state management
  private readonly isLoadingSignal = signal<boolean>(false);
  private readonly chatBoxVisibleSignal = signal<boolean>(false);

  // Computed properties
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly chatBoxVisible = computed(() => this.chatBoxVisibleSignal());

  // Anime character images (fallback to placeholder if not found)
  readonly characterImages = {
    aiAssistant: '/ai-assistant-avatar.png',
    mangaChar1: '/manga-char-1.png',
    mangaChar2: '/manga-char-2.png',
    mangaChar3: '/manga-char-3.png',
    searchCharacter: '/search-character.png',
    recommendCharacter: '/recommend-character.png',
    discussionCharacter: '/discussion-character.png',
    assistantCharacter: '/assistant-character.png',
    questionChar1: '/question-char-1.png',
    questionChar2: '/question-char-2.png',
    questionChar3: '/question-char-3.png',
    questionChar4: '/question-char-4.png',
    ctaCharacterLeft: '/cta-character-left.png',
    ctaCharacterRight: '/cta-character-right.png'
  };

  constructor(
    private seoService: SeoService,
    private urlService: UrlService,
    override  cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.setupSEO();
  }

  /**
   * Open AI Chatbox
   */
  openChatBox(): void {
    this.runInBrowser(() => {
      // Show the chat box bubble component
      if (ChatBoxBubbleComponent.Instance) {
        // Hide the bubble and show the chat box
        ChatBoxBubbleComponent.Instance.showChat();
        this.chatBoxVisibleSignal.set(true);
      } else {
        // If no instance available, show a message
        console.warn('ChatBox instance not available. Please ensure the chat-box-bubble component is loaded.');
      }
    });
  }

  /**
   * Handle image load error - fallback to placeholder
   */
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      // Use a generic anime placeholder or default avatar
      img.src = 'https://jbagy.me/wp-content/uploads/2025/03/Hinh-anh-anime-dang-yeu-khong-the-cuong-duoc-2.jpg';
      img.onerror = null; // Prevent infinite loop
    }
  }

  /**
   * Get character image with fallback
   */
  getCharacterImage(characterKey: keyof typeof this.characterImages): string {
    return this.characterImages[characterKey] || '/default-anime-avatar.png';
  }

  /**
   * Setup SEO for AI Assistant page
   */
  private setupSEO(): void {
    const title = 'Trợ lý AI - Chatbot thông minh cho truyện tranh | MeTruyenMoi';
    const description = 'Trợ lý AI thông minh giúp bạn tìm kiếm, gợi ý và trò chuyện về truyện tranh. Khám phá tính năng chatbot AI tiên tiến tại MeTruyenMoi.';
    const url = `${this.urlService.baseUrl}/tro-ly-ai`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      url,
      image: '/logo.png',
      siteName: 'MeTruyenMoi',
      canonical: url,
      twitterCard: 'summary_large_image' as const,
      keywords: 'trợ lý AI, chatbot, truyện tranh, AI assistant, comic chatbot, manga AI'
    };

    this.seoService.setSEOData(seoData);

    // Add structured data for AI Assistant page
    const pageSchema = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: 'Trợ lý AI',
      description: description,
      url: url,
      mainEntity: {
        '@type': 'SoftwareApplication',
        name: 'Trợ lý AI MeTruyenMoi',
        description: 'Chatbot AI thông minh cho truyện tranh',
        applicationCategory: 'ChatApplication',
        operatingSystem: 'Web Browser'
      }
    };

    this.seoService.addStructuredData([pageSchema]);
  }


}
