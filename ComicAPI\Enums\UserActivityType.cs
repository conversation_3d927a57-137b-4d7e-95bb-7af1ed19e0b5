namespace ComicAPI.Enums
{
    public enum UserActivityType
    {
        Login = 1,
        Logout = 2,
        ViewComic = 3,
        ReadChapter = 4,
        FollowComic = 5,
        UnfollowComic = 6,
        VoteComic = 7,
        AddComment = 8,
        DeleteComment = 9,
        UpdateProfile = 10,
        ChangePassword = 11,
        Search = 12,
        ViewGenre = 13,
        ViewTopComics = 14,
        ViewRecommendations = 15,
        ShareComic = 16,
        BookmarkChapter = 17,
        RemoveBookmark = 18,
        ViewNotifications = 19,
        MarkNotificationRead = 20,
        ViewUserProfile = 21,
        ViewComicDetails = 22,
        ViewChapterList = 23,
        ViewComments = 24,
        LikeComment = 25,
        ReplyComment = 26,
        ReportContent = 27,
        ViewHistory = 28,
        ClearHistory = 29,
        ExportData = 30
    }

    public enum ActivityPriority
    {
        Low = 1,      // Background activities, analytics
        Normal = 2,   // Regular user interactions
        High = 3,     // Important user actions
        Critical = 4  // Security-related activities
    }

    public enum SessionStatus
    {
        Active = 1,
        Inactive = 2,
        Expired = 3,
        Terminated = 4
    }
}
