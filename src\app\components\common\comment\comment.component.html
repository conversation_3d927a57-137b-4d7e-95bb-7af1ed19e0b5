<!-- Enhanced Comment Section - Matching Comic Website Design -->
<div class="comment-section-container">
  <!-- Login Prompt -->
  @if (!isLogin()) {
  <div class="login-prompt-wrapper">
    <div class="login-prompt-card">
      <svg class="login-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
        <polyline points="10,17 15,12 10,7"/>
        <line x1="15" y1="12" x2="3" y2="12"/>
      </svg>
      <div class="login-prompt-content">
        <h3 class="login-prompt-title">Tham gia thảo luận</h3>
        <p class="login-prompt-description">Đăng nhập để chia sẻ cảm nhận về truyện</p>
      </div>
      <a class="login-prompt-button" [routerLink]="['/auth/login']">
        Đ<PERSON><PERSON> nhập
        <svg class="login-button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <polyline points="9,18 15,12 9,6"/>
        </svg>
      </a>
    </div>
  </div>
  }

  <!-- Comments Header -->
  <div class="comments-header">
    <div class="comments-title-section">
      <div class="comments-title-wrapper">
        <svg class="comments-title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
        </svg>
        <h2 class="comments-title">Bình luận</h2>
        <span class="comments-count-badge">{{ commentsCount() }}</span>
      </div>
      <div class="comments-subtitle">Chia sẻ cảm nhận của bạn về truyện</div>
    </div>
  </div>
  <!-- Comment Form Section -->
  <div class="comments-content">
    @if (isLogin()) {
    <div class="comment-form-card">
      <form (submit)="onSubmit(formComment)" [formGroup]="formComment" class="comment-form">
        <div class="comment-form-header">
          <div class="form-user-avatar">
            <img
              loading="lazy"
              onerror="this.src='/default_avatar.jpg'"
              [src]="user()?.avatar"
              class="form-avatar-img"
              alt="avatar"
            />
            <div class="avatar-status-indicator"></div>
          </div>
          <div class="form-user-info">
            <span class="form-username">{{ user()?.firstName }} {{ user()?.lastName }}</span>
            <span class="form-user-role">Thành viên</span>
          </div>
        </div>

        <div class="comment-form-body">
          <textarea
            class="comment-textarea"
            name="content"
            placeholder="Chia sẻ cảm nhận của bạn về truyện này..."
            required
            formControlName="content"
            (input)="onInput(formComment)"
            rows="4"
          ></textarea>
        </div>

        <div class="comment-form-footer">
          <div class="form-tools">
            <div class="emoji-picker-container" (appClickOutside)="clickOutSite()">
              <button
                type="button"
                class="tool-button emoji-button"
                (click)="toggleEmojiPicker()"
                title="Thêm emoji"
              >
                <svg class="tool-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                  <line x1="9" y1="9" x2="9.01" y2="9"/>
                  <line x1="15" y1="9" x2="15.01" y2="9"/>
                </svg>
                <span class="tool-label">Emoji</span>
              </button>

              <div #emojiPickerContainer class="emoji-picker-dropdown">
                <div
                  *ngIf="activeEmojiPicker()"
                  app-emoji
                  (emojiSelect)="addEmoji($event, formComment)"
                ></div>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-button">
              Hủy
            </button>
            <button type="submit" class="submit-button" [disabled]="!formComment.valid">
              <svg class="submit-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="22" y1="2" x2="11" y2="13"/>
                <polygon points="22,2 15,22 11,13 2,9 22,2"/>
              </svg>
              Gửi bình luận
            </button>
          </div>
        </div>
      </form>
    </div>
    }

    <!-- Comments List -->
    <div class="comments-list">
      <div
        [@fadeInAnimation]="'in'"
        *ngFor="let comment of listComments(); trackBy: trackByCommentId"
        class="comment-item"
      >
        <ng-container
          [ngTemplateOutlet]="commentBlockTemplate"
          [ngTemplateOutletContext]="{ comment: comment, hide: false }"
        >
        </ng-container>

        <!-- Replies Section -->
        <div
          #ViewReplyEle
          [attr.reply-block]="comment.id"
          class="replies-container"
          [ngClass]="{
            'replies-expanded': replyId() === comment.id && !isViewReply()
          }"
        >
          <!-- Reply Items -->
          <div class="replies-list">
            <ng-container
              *ngFor="let reply of comment.replies; trackBy: trackByReplyId"
              [ngTemplateOutlet]="commentBlockTemplate"
              [ngTemplateOutletContext]="{ comment: reply, hide: true }"
            >
            </ng-container>
          </div>

          <!-- Reply Form -->
          @if (isLogin()) {
          <div class="reply-form-card" *ngIf="replyId() === comment.id">
            <form
              (submit)="onSubmit(formReply, comment.id)"
              [formGroup]="formReply"
              class="reply-form"
            >
              <div class="reply-form-header">
                <div class="reply-user-avatar">
                  <img
                    loading="lazy"
                    [src]="user()?.avatar"
                    onerror="this.src='/default_avatar.jpg'"
                    class="reply-avatar-img"
                    alt="avatar"
                  />
                </div>
                <div class="reply-form-content">
                  <textarea
                    class="reply-textarea"
                    name="content"
                    placeholder="Viết phản hồi..."
                    required
                    formControlName="content"
                    rows="3"
                  ></textarea>
                </div>
              </div>

              <div class="reply-form-footer">
                <div class="reply-tools">
                  <div
                    class="emoji-picker-container"
                    (appClickOutside)="activeEmojiPicker2.set(false)"
                  >
                    <button
                      type="button"
                      class="tool-button emoji-button"
                      (click)="toggleEmojiPicker2()"
                      title="Thêm emoji"
                    >
                      <svg class="tool-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                        <line x1="9" y1="9" x2="9.01" y2="9"/>
                        <line x1="15" y1="9" x2="15.01" y2="9"/>
                      </svg>
                    </button>

                    <div *ngIf="activeEmojiPicker2() === true" class="emoji-picker-dropdown">
                        <div
                        app-emoji
                        (emojiSelect)="addEmoji($event, formComment)"
                        ></div>
                    </div>
                  </div>
                </div>

                <div class="reply-actions">
                  <button type="button" class="cancel-reply-button" (click)="replyId.set(-1)">
                    Hủy
                  </button>
                  <button type="submit" class="submit-reply-button">
                    <svg class="submit-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <line x1="22" y1="2" x2="11" y2="13"/>
                      <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                    </svg>
                    Gửi
                  </button>
                </div>
              </div>
            </form>
          </div>
          }
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div class="comments-pagination" *ngIf="showPagination()">
    <div
      app-pagination
      [currentPage]="currentPage()"
      [totalpage]="totalPages()"
      (OnChange)="OnChangePage($event)"
    ></div>
  </div>
</div>

<!-- Enhanced Comment Block Template -->
<ng-template #commentBlockTemplate let-comment="comment" let-hide="hide">
  <div class="comment-block" [id]="'id' + comment.id" [class.reply-comment]="hide">
    <div class="comment-main flex gap-3">
      <div class="comment-avatar-section">
        <div (click)="ViewInfoUser(comment.userID)" class="comment-user-avatar">
          <img
            loading="lazy"
            [src]="comment?.avatar"
            class="comment-avatar-img"
            onerror="this.src='/default_avatar.jpg'"
            alt="avatar"
          />
          <div class="avatar-status" *ngIf="!hide"></div>
        </div>
      </div>

      <div class="comment-body">
        <div class="comment-header">
          <div class="comment-user-info">
            <span class="comment-username">{{ comment.userName }}</span>
            <span class="comment-user-badge" *ngIf="!hide">Thành viên</span>
          </div>
          <div class="comment-meta">
            <span class="comment-date" *ngIf="hide === false">
              {{ comment.commentedAt | dateAgo }}
            </span>
            <a
              [routerLink]="getChapterLink(comment)"
              class="comment-chapter-link"
              *ngIf="comment.chapterName"
            >
              Chapter {{ comment.chapterName }}
            </a>
          </div>
        </div>

        <div class="comment-content-text" [innerHTML]="comment.content | emojiParser"></div>

        <div class="comment-footer">
          <div class="comment-reactions">
            <button class="reaction-button like-button">
              <svg class="reaction-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/>
              </svg>
              <span class="reaction-count">0</span>
            </button>
            <button class="reaction-button dislike-button">
              <svg class="reaction-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"/>
              </svg>
            </button>
          </div>

          <div class="comment-actions">
            <button class="action-button reply-button" (click)="replyCmt(comment, hide)">
              <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              </svg>
              Phản hồi
            </button>
          </div>
        </div>

        <!-- View Replies Button -->
        <div class="view-replies-section" *ngIf="comment.replies && comment.replies.length > 0 && !hide">
          <button class="view-replies-button" (click)="ViewReplyCmt(comment.id)">
            <svg class="view-replies-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="6,9 12,15 18,9"/>
            </svg>
            Xem {{ comment.replies.length }} phản hồi
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
