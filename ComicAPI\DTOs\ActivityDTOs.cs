using ComicAPI.Enums;

namespace ComicAPI.DTOs
{
    public class UserActivityDTO
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public UserActivityType ActivityType { get; set; }
        public string? EntityType { get; set; }
        public int? EntityId { get; set; }
        public string? Metadata { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? SessionId { get; set; }
    }

    public class UserReadingHistoryDTO
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int ComicId { get; set; }
        public string ComicTitle { get; set; } = string.Empty;
        public string ComicCoverImage { get; set; } = string.Empty;
        public int ChapterId { get; set; }
        public string ChapterTitle { get; set; } = string.Empty;
        public float ChapterUrl { get; set; }
        public int? PageNumber { get; set; }
        public float ReadingProgress { get; set; }
        public int ReadingTimeSeconds { get; set; }
        public DateTime FirstReadAt { get; set; }
        public DateTime LastReadAt { get; set; }
        public int ReadCount { get; set; }
        public bool IsCompleted { get; set; }
    }

    public class UserLoginHistoryDTO
    {
        public int Id { get; set; }
        public DateTime LoginAt { get; set; }
        public DateTime? LogoutAt { get; set; }
        public string? IpAddress { get; set; }
        public string? DeviceInfo { get; set; }
        public string? Location { get; set; }
        public int? SessionDurationSeconds { get; set; }
        public bool IsSuccessful { get; set; }
    }

    public class UserSessionActivityDTO
    {
        public int Id { get; set; }
        public string SessionId { get; set; } = string.Empty;
        public DateTime StartedAt { get; set; }
        public DateTime LastActivityAt { get; set; }
        public DateTime? EndedAt { get; set; }
        public int TotalDurationSeconds { get; set; }
        public int PagesVisited { get; set; }
        public int ComicsViewed { get; set; }
        public int ChaptersRead { get; set; }
        public bool IsActive { get; set; }
    }

    public class UserActivityStatsDTO
    {
        public int TotalComicsRead { get; set; }
        public int TotalChaptersRead { get; set; }
        public int TotalReadingTimeHours { get; set; }
        public int TotalLoginDays { get; set; }
        public int CurrentStreak { get; set; }
        public int LongestStreak { get; set; }
        public DateTime? LastActivity { get; set; }
        public List<GenreStatsDTO> FavoriteGenres { get; set; } = new();
        public List<DailyActivityDTO> DailyActivity { get; set; } = new();
        public List<MonthlyActivityDTO> MonthlyActivity { get; set; } = new();
    }

    public class GenreStatsDTO
    {
        public int GenreId { get; set; }
        public string GenreName { get; set; } = string.Empty;
        public int ComicsRead { get; set; }
        public int ChaptersRead { get; set; }
        public int ReadingTimeHours { get; set; }
    }

    public class DailyActivityDTO
    {
        public DateTime Date { get; set; }
        public int ChaptersRead { get; set; }
        public int ReadingTimeMinutes { get; set; }
        public int LoginCount { get; set; }
        public bool IsActive { get; set; }
    }

    public class MonthlyActivityDTO
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public int TotalChaptersRead { get; set; }
        public int TotalReadingTimeHours { get; set; }
        public int ActiveDays { get; set; }
        public int NewComicsStarted { get; set; }
        public int ComicsCompleted { get; set; }
    }

    public class ActivityTrackingRequest
    {
        public UserActivityType ActivityType { get; set; }
        public string? EntityType { get; set; }
        public int? EntityId { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
        public ActivityPriority Priority { get; set; } = ActivityPriority.Normal;
    }

    public class ReadingProgressRequest
    {
        public int ComicId { get; set; }
        public int ChapterId { get; set; }
        public int? PageNumber { get; set; }
        public float ReadingProgress { get; set; }
        public int ReadingTimeSeconds { get; set; }
        public bool IsCompleted { get; set; } = false;
    }

    public class ActivityQueryRequest
    {
        public int? UserId { get; set; }
        public UserActivityType? ActivityType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? EntityType { get; set; }
        public int? EntityId { get; set; }
    }
}
