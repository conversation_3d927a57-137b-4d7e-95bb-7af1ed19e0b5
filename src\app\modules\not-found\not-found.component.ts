import { Location } from '@angular/common';
import { Component, Inject, OnInit, Optional, PLATFORM_ID, RESPONSE_INIT } from '@angular/core';
import { Router } from '@angular/router';
import { SeoService } from '@services/seo.service';

@Component({
    selector: 'app-not-found',
    templateUrl: './not-found.component.html',
    styleUrl: './not-found.component.scss',
    standalone: false
})
export class NotFoundComponent implements OnInit {
  searchQuery: string = '';

  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    @Optional() @Inject(RESPONSE_INIT) private response: ResponseInit,
    private seoService: SeoService,
    private router: Router,
    private location: Location
  ) {
    this.setupSEO();
  }

  ngOnInit() {
    if(this.response) {
      this.response.status = 404;
    }
  }

  /**
   * Setup SEO meta tags for 404 page
   */
  private setupSEO(): void {
    const title = '404 - Trang không tồn tại | <PERSON>ê Truyện <PERSON>ới';
    const description = 'Trang bạn đang tìm kiếm không tồn tại. Khám phá những bộ truyện tranh hay nhất tại Mê Truyện Mới.';

    this.seoService.setTitle(title);
    this.seoService.addTags([
      { name: 'description', content: description },
      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      { property: 'og:type', content: 'website' },
      { property: 'og:site_name', content: 'MeTruyenMoi' },
      { itemprop: 'name', content: title },
      { itemprop: 'description', content: description },
    ]);
  }

  /**
   * Navigate back to previous page
   */
  goBack(): void {
    this.location.back();
  }

  /**
   * Perform search with current query
   */
  performSearch(): void {
    if (this.searchQuery.trim()) {
      this.router.navigate(['/tim-kiem'], {
        queryParams: { q: this.searchQuery.trim() }
      });
    }
  }
}
