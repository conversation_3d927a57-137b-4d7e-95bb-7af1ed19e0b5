using ComicAPI.DTOs;
using ComicAPI.Enums;
using ComicAPI.Models.Activity;

namespace ComicAPI.Services
{
    public interface IUserActivityService
    {
        // Activity Tracking
        Task TrackActivityAsync(int userId, UserActivityType activityType, string? entityType = null, 
            int? entityId = null, Dictionary<string, object>? metadata = null, 
            ActivityPriority priority = ActivityPriority.Normal);
        
        void TrackActivityBackground(int userId, UserActivityType activityType, string? entityType = null, 
            int? entityId = null, Dictionary<string, object>? metadata = null, 
            ActivityPriority priority = ActivityPriority.Normal);

        // Reading History
        Task TrackReadingProgressAsync(int userId, int comicId, int chapterId, 
            int? pageNumber = null, float readingProgress = 0, int readingTimeSeconds = 0, 
            bool isCompleted = false);
        
        void TrackReadingProgressBackground(int userId, int comicId, int chapterId, 
            int? pageNumber = null, float readingProgress = 0, int readingTimeSeconds = 0, 
            bool isCompleted = false);

        // Login/Session Tracking
        Task TrackLoginAsync(int userId, string? ipAddress = null, string? userAgent = null, 
            string? deviceInfo = null, string? location = null, bool isSuccessful = true, 
            string? failureReason = null);
        
        Task TrackLogoutAsync(int userId, string? sessionId = null);
        
        Task<string> StartSessionAsync(int userId, string? ipAddress = null, string? userAgent = null);
        
        Task UpdateSessionActivityAsync(string sessionId, bool incrementPages = false, 
            bool incrementComics = false, bool incrementChapters = false);
        
        Task EndSessionAsync(string sessionId);

        // User Activity Updates
        Task UpdateUserLastActivityAsync(int userId);
        
        void UpdateUserLastActivityBackground(int userId);

        // Query Methods
        Task<List<UserActivityDTO>> GetUserActivitiesAsync(int userId, int page = 1, int pageSize = 20, 
            UserActivityType? activityType = null, DateTime? fromDate = null, DateTime? toDate = null);
        
        Task<List<UserReadingHistoryDTO>> GetUserReadingHistoryAsync(int userId, int page = 1, int pageSize = 20, 
            int? comicId = null, bool? isCompleted = null);
        
        Task<List<UserLoginHistoryDTO>> GetUserLoginHistoryAsync(int userId, int page = 1, int pageSize = 20, 
            DateTime? fromDate = null, DateTime? toDate = null);
        
        Task<UserActivityStatsDTO> GetUserActivityStatsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);

        // Analytics
        Task<List<DailyActivityDTO>> GetDailyActivityAsync(int userId, DateTime fromDate, DateTime toDate);
        
        Task<List<MonthlyActivityDTO>> GetMonthlyActivityAsync(int userId, int year);
        
        Task<List<GenreStatsDTO>> GetUserGenreStatsAsync(int userId);

        // Batch Processing
        Task ProcessPendingActivitiesAsync();
        
        Task CleanupOldActivitiesAsync(int daysToKeep = 365);

        // Performance Methods
        Task<bool> IsUserActiveAsync(int userId, TimeSpan timeWindow);
        
        Task<DateTime?> GetUserLastActivityAsync(int userId);
        
        Task<int> GetUserActiveSessionsCountAsync(int userId);
    }
}
