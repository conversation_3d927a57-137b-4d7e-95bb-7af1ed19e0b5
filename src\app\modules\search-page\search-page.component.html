<app-breadcrumb
  class="z-10 my-2 container mx-auto flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Tìm truyện', url: '' }
  ]"
>
</app-breadcrumb>

<div
  id="content"
  class="md:container mx-auto mt-5"
  (keydown.enter)="performSearch(); selectOptions.genres.isShow = false"
>
  <div class="grid gap-2 md:grid-cols-[1fr_12rem] mx-2 lg:mx-0 dark:text-white">
    <form class="flex items-center relative">
      <div class="absolute left-1 p-2 rounded-lg">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="none"
          viewBox="0 0 24 24"
          class="icon text-icon-contrast text-neutral-700 dark:text-neutral-500"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M11 19a8 8 0 1 0 0-16 8 8 0 0 0 0 16m10 2-4.35-4.35"
          ></path>
        </svg>
      </div>
      <input
        class="placeholder-current bg-neutral-200 dark:bg-neutral-700 w-full h-10 rounded-lg pl-12 pr-4 focus:outline-primary-100 focus:bg-slate-100 dark:focus:bg-dark-bg outline-none"
        type="search"
        placeholder="Nhập tên truyện..."
        title="Search"
        [(ngModel)]="selectOptions.keyword.value"
        name="keyword"
      />
      <div class="md-search"></div>
    </form>

    <!-- Show Filters Button -->
    <button
      (click)="toggleFilters()"
      class="rounded-lg relative flex items-center overflow-hidden accent text-white"
      style="min-height: 2.5rem; min-width: 100%"
      title="Show Filters"
      [ngClass]="{
        'bg-primary-50 hover:opacity-90': showFilters,
        'bg-primary-100  hover:bg-primary-200': !showFilters
      }"
    >
      <span
        class="flex relative items-center justify-center font-medium select-none w-full"
        style="pointer-events: none; justify-content: center"
      >
        <svg
          class="h-4 w-4"
          [ngClass]="{
            'fill-white': showFilters
          }"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
        </svg>

        <p class="mx-2">{{ showFilters ? 'Ẩn bộ lọc' : 'Thêm bộ lọc' }}</p>
      </span>
    </button>
  </div>
  <div class="grid grid-cols-[1fr_5rem] border-b border-b-accent-10 dark:border-b-neutral-800">
    <ul class="flex flex-wrap gap-2 py-2 mx-1 overflow-hidden">
      <li
        *ngIf="selectOptions.sorts.value !== -1"
        class="px-2 border border-primary-100 text-primary-100 rounded-full select-none cursor-pointer flex"
      >
        <svg
          *ngIf="selectOptions.sorts.value !== lastupdate"
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          class="feather feather-x icon small text-icon-contrast text-undefined my-auto"
          viewBox="0 0 24 24"
          (click)="RemoveFilterTag({ option: 'sort', data: selectOptions.sorts })"
        >
          <path d="M18 6 6 18M6 6l12 12"></path>
        </svg>
        <span class="text-sm">{{ selectOptions.sorts.label }}</span>
      </li>
      <li
        *ngIf="selectOptions.status.value !== -1"
        class="border border-primary-100 text-primary-100 rounded-full px-2 cursor-pointer flex space-x-1 whitespace-nowrap"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          class="feather feather-x icon small text-icon-contrast text-undefined my-auto"
          viewBox="0 0 24 24"
          (click)="RemoveFilterTag({ option: 'status', data: selectOptions.status })"
        >
          <path d="M18 6 6 18M6 6l12 12"></path>
        </svg>
        <span class="text-sm">{{ selectOptions.status.label }}</span>
      </li>

      <li
        *ngFor="let key of getGenreKeys(); trackBy: trackByIndex"
        class="px-2 rounded-full select-none cursor-pointer flex space-x-1 whitespace-nowrap"
        [ngClass]="{
          ' bg-neutral-100 ': !selectOptions.genres.value[key],
          'text-red-500 line-through outline-1 outline-dashed outline-red-500 ':
            selectOptions.genres.value[key] === 2,
          'text-primary-100 border border-primary-100 ': selectOptions.genres.value[key] === 1
        }"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          class="feather feather-x icon small text-icon-contrast text-undefined my-auto"
          viewBox="0 0 24 24"
          (click)="
            RemoveFilterTag({
              option: 'genres',
              data: key
            })
          "
        >
          <path d="M18 6 6 18M6 6l12 12"></path>
        </svg>
        <span class="text-sm">{{ selectOptions.genres.label[key] }}</span>
      </li>
      <li
        *ngIf="selectOptions.year.value > 0"
        class="px-2 rounded-full border border-primary-100 text-primary-100 cursor-pointer flex space-x-1 whitespace-nowrap"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          class="feather feather-x icon small text-icon-contrast text-undefined my-auto"
          viewBox="0 0 24 24"
          (click)="RemoveFilterTag({ option: 'year', data: selectOptions.year })"
        >
          <path d="M18 6 6 18M6 6l12 12"></path>
        </svg>
        <span class="text-sm">{{ selectOptions.year.value }}</span>
      </li>
    </ul>
    <div class="flex justify-end text-primary-100 gap-2 m-2 lg:m-2 text-xs" *ngIf="showFilters">
      <!-- <button
        class="font-semibold text-center text-primary-100 inline-flex"
        (click)="saveFilters()"
      >
        <span>Lưu</span>
      </button>
      | -->
      <button
        class="font-semibold text-center text-primary-100 inline-flex gap-1"
        (click)="clearAllFilter()"
      >
        <svg
          class="h-4 w-4"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="3 6 5 6 21 6" />
          <path
            d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
          />
          <line x1="10" y1="11" x2="10" y2="17" />
          <line x1="14" y1="11" x2="14" y2="17" />
        </svg>
        <span>Xóa</span>
      </button>
    </div>
  </div>

  <!-- Filter Options -->
  <div [ngClass]="{ hidden: !showFilters }" class="mx-2 lg:mx-0 grid grid-rows-[1fr_0fr]">
    <div
      class="grid grid-cols-2 max-sm:grid-cols-1 md:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-5 gap-8 my-4 max-sm:gap-4"
    >
      <div class="relative space-y-1">
        <label
          id="headlessui-listbox-label-1"
          data-headlessui-state=""
          class="xl:text-sm text-xs text-neutral-600 dark:text-neutral-400"
          >Sắp xếp theo</label
        >
        <app-selection-2
          class="flex"
          [options]="dataView.sorts"
          [selectedValue]="selectOptions.sorts.value"
          (selectedValueChange)="OnFilterChange1('sorts', $event)"
        >
        </app-selection-2>
      </div>

      <div class="relative space-y-1" (appClickOutside)="selectOptions.genres.isShow = false">
        <label class="text-midTone xl:text-sm text-xs text-neutral-600 dark:text-neutral-400"
          >Thể loại
        </label>
        <button
          (click)="selectOptions.genres.isShow = !selectOptions.genres.isShow"
          class="w-full grid grid-cols-[1fr_1rem] px-2 py-1 rounded-md transition-[background-color,outline-color] outline outline-1 outline-transparent focus:outline-primary font-medium bg-neutral-100 dark:bg-neutral-700"
          type="button"
          [ngClass]="{
            'outline outline-primary-100': selectOptions.genres.isShow,
            'hover:outline hover:outline-primary-100': !selectOptions.genres.isShow
          }"
        >
          @if (getGenreKeys().length > 0) {
          <p class="font-semibold line-clamp-1 text-left dark:text-white">
            {{ getJoinedGenreNames() }}
          </p>
          } @else {
          <span class="truncate text-left font-semibold dark:text-white">Thể loại</span>
          }

          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-5 h-5 my-auto text-neutral-500"
            viewBox="0 0 24 24"
          >
            <path
              fill="none"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="m8 9l4-4l4 4m0 6l-4 4l-4-4"
            ></path>
          </svg>
        </button>
        <div
          [ngClass]="{
            hidden: !selectOptions.genres.isShow
          }"
          class="absolute mt-1 top-full left-0 bg-white z-20 text-black shadow-sm"
        >
          <app-genre-catagories
            [routerLinkGenres]="false"
            (clickGenres)="OnGenresChange($event)"
            [statusGenres]="this.selectOptions.genres.value"
          ></app-genre-catagories>
        </div>
      </div>

      <div
        hidden=""
        style="
          position: fixed;
          height: 0px;
          padding: 0px;
          overflow: hidden;
          clip: rect(0px, 0px, 0px, 0px);
          white-space: nowrap;
          border-width: 0px;
          display: none;
        "
      ></div>

      <label class="truncate xl:text-base text-sm relative space-y-1 grid">
        <span
          class="text-neutral-600 xl:text-sm text-xs dark:text-neutral-400"
          title="Publication year"
          >Năm phát hành</span
        >
        <div
          class="grid grid-cols-[1fr_3rem] gap-2 rounded-md outline outline-0 outline-transparent bg-neutral-100 dark:bg-neutral-700 pointer-events-none"
        >
          <input
            class="appearance-none font-semibold px-2 py-[0.3rem] bg-neutral-100 dark:text-white dark:bg-neutral-700 rounded-md text-color outline-none outline-1 -outline-offset-1 transition-[outline-color] w-full pointer-events-auto placeholder:text-color focus:outline-primary"
            type="number"
            required
            minlength="4"
            maxlength="4"
            min="1900"
            max="4000"
            [ngClass]="{
              'outline outline-primary-100': selectOptions.year.isShow,
              'hover:outline hover:outline-primary-100': !selectOptions.year.isShow
            }"
            [(ngModel)]="selectOptions.year.value"
            (ngModelChange)="OnYearChange(0)"
            inputmode="numeric"
            placeholder="Năm phát hành"
          />

          <div class="flex my-auto gap-2 pointer-events-auto mr-2">
            <svg
              data-input-counter-increment="quantity-input"
              data-v-5cba5096=""
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              fill="none"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              class="feather feather-minus icon small text-icon-contrast text-undefined hover:text-primary cursor-pointer dark:text-white"
              viewBox="0 0 24 24"
              (click)="OnYearChange(-1)"
            >
              <path d="M5 12h14"></path>
            </svg>

            <svg
              data-v-5cba5096=""
              data-v-eb114e73=""
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              fill="none"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              class="feather feather-plus icon small text-icon-contrast text-undefined hover:text-primary cursor-pointer dark:text-white"
              viewBox="0 0 24 24"
              (click)="OnYearChange(+1)"
            >
              <path d="M12 5v14m-7-7h14"></path>
            </svg>
          </div>
        </div>
      </label>
      <div class="relative space-y-1">
        <label class="text-neutral-600 truncate xl:text-base text-sm"
          ><span title="Publication Status " class="xl:text-sm text-xs dark:text-neutral-400"
            >Trạng thái</span
          ></label
        >
        <app-selection-2
          class="flex"
          [options]="dataView.status"
          [selectedValue]="selectOptions.status.value"
          (selectedValueChange)="OnFilterChange1('status', $event)"
        >
        </app-selection-2>
      </div>
    </div>
  </div>
  <div class="w-full flex justify-start m-2 lg:mx-auto">
    <button
      class="bg-primary-100 hover:bg-primary-200 transition-all ease-in text-white font-bold py-2 px-4 rounded flex items-center"
      (click)="performSearch(); selectOptions.genres.isShow = false"
    >
      <svg
        class="h-6 w-6 mr-2"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <circle cx="10" cy="10" r="7" />
        <line x1="21" y1="21" x2="15" y2="15" />
      </svg>
      <span>Tìm kiếm</span>
      <!-- <svg
        class="h-6 w-6"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <polyline points="13 17 18 12 13 7" />
        <polyline points="6 17 11 12 6 7" />
      </svg> -->
    </button>
  </div>
  <div *ngIf="isLoading; else searchResults">
    <app-spinner [sizeSpinner]="'40'"></app-spinner>
  </div>
  <ng-template #searchResults>
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-4">
      <div id="listComic" class="col-span-1 xl:col-span-3 row-start-1">
        <app-grid-comic
          [listComics]="listComics"
          *ngIf="this.listComics.length > 0 || this.isSearching; else empty"
          [nPreview]="nPreview"
          [title]="'(' + this.totalResult + ') kết quả'"
        >
        </app-grid-comic>
        <app-pagination
          (OnChange)="OnChangePage($event)"
          [currentPage]="currentPage"
          [totalpage]="totalpage"
        >
        </app-pagination>
      </div>
      <div class="col-span-1">
        <app-top-list class="my-4 block"></app-top-list>
      </div>
    </div>

    <ng-template #empty class="">
      <div class="col-span-3 w-1/3 mt-20 lg:min-h-1/2 lg:w-40 mx-auto">
        <app-empty [message]="'Không tìm thấy truyện'"></app-empty>
      </div>
    </ng-template>
  </ng-template>
</div>
