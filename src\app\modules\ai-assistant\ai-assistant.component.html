<div class="ai-assistant-container">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <div class="hero-characters">
        <!-- AI Assistant Character -->
        <div class="ai-character">
          <div class="character-avatar">
            <img [src]="getCharacterImage('aiAssistant')"
                 alt="AI Assistant"
                 class="character-image"
                 loading="eager"
                 (error)="onImageError($event)">
          </div>
          <div class="speech-bubble">
            <p>Xin chào! Tôi là trợ lý AI của bạn. Hãy hỏi tôi bất cứ điều gì về truyện tranh nhé! 📚✨</p>
          </div>
        </div>

        <!-- Supporting Characters -->
        <div class="supporting-characters">
          <div class="character-float char-1">
            <img [src]="getCharacterImage('mangaChar1')"
                 alt="Manga Character 1"
                 class="mini-character"
                 loading="lazy"
                 (error)="onImageError($event)">
          </div>
          <div class="character-float char-2">
            <img [src]="getCharacterImage('mangaChar2')"
                 alt="Manga Character 2"
                 class="mini-character"
                 loading="lazy"
                 (error)="onImageError($event)">
          </div>
          <div class="character-float char-3">
            <img [src]="getCharacterImage('mangaChar3')"
                 alt="Manga Character 3"
                 class="mini-character"
                 loading="lazy"
                 (error)="onImageError($event)">
          </div>
        </div>
      </div>

      <h1 class="hero-title gradient-text">Trợ lý AI thông minh</h1>
      <p class="hero-subtitle">
        Khám phá thế giới truyện tranh với sự hỗ trợ của AI. Tìm kiếm, gợi ý và trò chuyện về những bộ truyện yêu thích của bạn cùng các nhân vật anime đáng yêu!
      </p>
      <button class="cta-button" (click)="openChatBox()">
        <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
        </svg>
        Bắt đầu trò chuyện với AI
      </button>
    </div>
  </section>


</div>
