<div class="ai-assistant-container">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <div class="hero-icon">
        <svg class="ai-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
        </svg>
      </div>
      <h1 class="hero-title gradient-text"><PERSON><PERSON><PERSON> lý AI thông minh</h1>
      <p class="hero-subtitle">
        <PERSON><PERSON><PERSON><PERSON> phá thế giới truyện tranh với sự hỗ trợ của AI. <PERSON><PERSON><PERSON> k<PERSON>, gợ<PERSON> <PERSON> và trò chuyện về nh<PERSON>ng bộ truyện yêu thích của bạn.
      </p>
      <button class="cta-button" (click)="openChatBox()">
        <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
        </svg>
        Bắt đầu trò chuyện với AI
      </button>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features-section">
    <div class="features-container">
      <h2 class="section-title">Tính năng nổi bật</h2>
      <div class="features-grid">
        
        <!-- Feature 1: Smart Search -->
        <div class="feature-card">
          <div class="feature-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <h3 class="feature-title">Tìm kiếm thông minh</h3>
          <p class="feature-description">
            AI hiểu ngữ cảnh và giúp bạn tìm truyện chính xác theo mô tả, thể loại hoặc cảm xúc bạn muốn.
          </p>
        </div>

        <!-- Feature 2: Personalized Recommendations -->
        <div class="feature-card">
          <div class="feature-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
          </div>
          <h3 class="feature-title">Gợi ý cá nhân hóa</h3>
          <p class="feature-description">
            Dựa trên sở thích đọc của bạn, AI sẽ gợi ý những bộ truyện phù hợp và thú vị nhất.
          </p>
        </div>

        <!-- Feature 3: Comic Discussion -->
        <div class="feature-card">
          <div class="feature-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"/>
            </svg>
          </div>
          <h3 class="feature-title">Thảo luận truyện tranh</h3>
          <p class="feature-description">
            Trò chuyện về cốt truyện, nhân vật, và những chi tiết thú vị trong các bộ truyện yêu thích.
          </p>
        </div>

        <!-- Feature 4: Reading Assistant -->
        <div class="feature-card">
          <div class="feature-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
          </div>
          <h3 class="feature-title">Trợ lý đọc truyện</h3>
          <p class="feature-description">
            Hỗ trợ giải thích nội dung, tóm tắt chương, và theo dõi tiến độ đọc của bạn.
          </p>
        </div>

      </div>
    </div>
  </section>

  <!-- How to Use Section -->
  <section class="how-to-use-section">
    <div class="how-to-container">
      <h2 class="section-title">Cách sử dụng</h2>
      <div class="steps-grid">
        
        <div class="step-card">
          <div class="step-number">1</div>
          <h3 class="step-title">Mở Chatbox</h3>
          <p class="step-description">
            Click vào nút "Bắt đầu trò chuyện" hoặc biểu tượng chat ở góc màn hình.
          </p>
        </div>

        <div class="step-card">
          <div class="step-number">2</div>
          <h3 class="step-title">Đặt câu hỏi</h3>
          <p class="step-description">
            Hỏi AI về truyện tranh, yêu cầu gợi ý hoặc thảo luận về nội dung.
          </p>
        </div>

        <div class="step-card">
          <div class="step-number">3</div>
          <h3 class="step-title">Nhận phản hồi</h3>
          <p class="step-description">
            AI sẽ trả lời thông minh và cung cấp thông tin hữu ích cho bạn.
          </p>
        </div>

      </div>
    </div>
  </section>




</div>
