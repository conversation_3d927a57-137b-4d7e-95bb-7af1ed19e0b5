// AI Assistant Page Styles
.ai-assistant-container {
  @apply bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800;
}

// Hero Section
.hero-section {
  padding: 5rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 64rem;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

.hero-characters {
  margin-bottom: 3rem;
  position: relative;
}

.ai-character {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;

  .character-avatar {
    position: relative;
    margin-bottom: 1rem;

    .character-image {
      width: 8rem;
      height: 8rem;
      border-radius: 50%;
      border: 4px solid #93c5fd;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      animation: float 3s ease-in-out infinite;

      .dark & {
        border-color: #2563eb;
      }
    }
  }

  .speech-bubble {
    background: white;
    padding: 1rem;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 24rem;

    .dark & {
      background: #374151;
    }

    &::before {
      content: '';
      position: absolute;
      top: -0.5rem;
      left: 50%;
      transform: translateX(-50%) rotate(45deg);
      width: 1rem;
      height: 1rem;
      background: white;

      .dark & {
        background: #374151;
      }
    }

    p {
      color: #374151;
      font-size: 0.875rem;
      font-weight: 500;
      margin: 0;

      .dark & {
        color: #d1d5db;
      }
    }
  }
}

.supporting-characters {
  position: absolute;
  inset: 0;
  pointer-events: none;

  .character-float {
    position: absolute;

    .mini-character {
      width: 4rem;
      height: 4rem;
      border-radius: 50%;
      opacity: 0.7;
    }

    &.char-1 {
      top: 2.5rem;
      left: 2.5rem;
      animation: float 4s ease-in-out infinite;
    }

    &.char-2 {
      top: 5rem;
      right: 2.5rem;
      animation: float 3.5s ease-in-out infinite 0.5s;
    }

    &.char-3 {
      bottom: 5rem;
      left: 5rem;
      animation: float 4.5s ease-in-out infinite 1s;
    }
  }
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1.5rem;

  @media (min-width: 640px) {
    font-size: 3.75rem;
  }

  .dark & {
    color: white;
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  margin-bottom: 2.5rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.625;

  .dark & {
    color: #d1d5db;
  }
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: #3b82f6;
  color: white;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.2s;
  transform: scale(1);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;

  &:hover {
    background: #1d4ed8;
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .button-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
}

// Features Section
.features-section {
  padding: 5rem 1rem;
  background: white;

  .dark & {
    background: #1f2937;
  }
}

.features-container {
  max-width: 72rem;
  margin: 0 auto;
}

.section-title {
  font-size: 2.25rem;
  font-weight: bold;
  text-align: center;
  color: #111827;
  margin-bottom: 4rem;

  .dark & {
    color: white;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.feature-card {
  background: #f9fafb;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  transition: all 0.3s;
  transform: translateY(0);
  position: relative;

  .dark & {
    background: #374151;
  }

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateY(-0.5rem);
  }
}

.feature-character {
  position: absolute;
  top: -1rem;
  right: -1rem;

  .feature-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    .dark & {
      border-color: #374151;
    }
  }
}

.feature-icon {
  @apply mb-6 flex justify-center;
  
  svg {
    @apply w-12 h-12 text-primary-100 dark:text-blue-400;
  }
}

.feature-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-4;
}

.feature-description {
  @apply text-gray-600 dark:text-gray-300 leading-relaxed;
}

// How to Use Section
.how-to-use-section {
  @apply py-20 px-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800;
}

.how-to-container {
  @apply max-w-5xl mx-auto;
}

.steps-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-8;
}

.step-card {
  @apply bg-white dark:bg-gray-700 p-8 rounded-2xl text-center shadow-md hover:shadow-lg transition-all duration-300;
}

.step-number {
  @apply w-12 h-12 bg-primary-100 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6;
}

.step-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-4;
}

.step-description {
  @apply text-gray-600 dark:text-gray-300 leading-relaxed;
}


.question-icon {
  @apply mb-4 flex justify-start;

  svg {
    @apply w-6 h-6 text-primary-100 dark:text-blue-400;
  }
}

.question-text {
  @apply text-gray-700 dark:text-gray-300 font-medium;
}


// Responsive Design
@screen sm {
  .hero-title {
    @apply text-5xl;
  }
  
  .features-grid {
    @apply grid-cols-2;
  }
}

@screen lg {
  .features-grid {
    @apply grid-cols-4;
  }
  
  .steps-grid {
    @apply grid-cols-3;
  }
}

// Dark Mode Enhancements
@media (prefers-color-scheme: dark) {
  .ai-assistant-container {
    @apply from-gray-900 to-gray-800;
  }
  
  .features-section {
    @apply bg-gray-800;
  }
  
  .how-to-use-section {
    @apply from-gray-900 to-gray-800;
  }
}

// Demo Questions Section
.demo-section {
  padding: 5rem 1rem;
  background: white;

  .dark & {
    background: #1f2937;
  }
}

.demo-container {
  max-width: 80rem;
  margin: 0 auto;
}

.demo-subtitle {
  font-size: 1.125rem;
  color: #4b5563;
  text-align: center;
  margin-bottom: 3rem;

  .dark & {
    color: #d1d5db;
  }
}

.demo-questions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.demo-question {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 1rem;

  .dark & {
    background: #374151;
  }

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    background: #eff6ff;
    border-color: #bfdbfe;
    transform: translateY(-0.25rem);

    .dark & {
      background: #4b5563;
      border-color: #60a5fa;
    }
  }
}

.question-character {
  flex-shrink: 0;

  .question-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    border: 2px solid #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

.question-content {
  flex: 1;

  .question-icon {
    margin-bottom: 0.5rem;

    svg {
      width: 1.5rem;
      height: 1.5rem;
      color: #3b82f6;

      .dark & {
        color: #60a5fa;
      }
    }
  }

  .question-text {
    color: #374151;
    font-weight: 500;
    margin: 0;

    .dark & {
      color: #d1d5db;
    }
  }
}

// CTA Section
.cta-section {
  padding: 5rem 1rem;
  background: linear-gradient(to right, #3b82f6, #6366f1);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-container {
  max-width: 64rem;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

.cta-characters {
  position: absolute;
  inset: 0;
  pointer-events: none;

  .cta-character-left {
    position: absolute;
    bottom: 0;
    left: 2rem;
    width: 8rem;
    height: auto;
    opacity: 0.8;
    animation: float 4s ease-in-out infinite;
  }

  .cta-character-right {
    position: absolute;
    bottom: 0;
    right: 2rem;
    width: 8rem;
    height: auto;
    opacity: 0.8;
    animation: float 3s ease-in-out infinite 1s;
  }
}

.cta-title {
  font-size: 2.25rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.cta-description {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.625;
}

.cta-button-large {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 2.5rem;
  background: white;
  color: #3b82f6;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.2s;
  transform: scale(1);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .button-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
}

// Animation Classes
.fade-in {
  opacity: 0;
  transform: translateY(1rem);
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-0.5rem);
  }
  70% {
    transform: translateY(-0.25rem);
  }
  90% {
    transform: translateY(-0.125rem);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-0.5rem);
  }
}

// Hover Effects
.feature-card:hover {
  transform: translateY(-0.5rem);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: bounce 0.6s ease-in-out;
}

.step-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

// Loading State
.loading {
  opacity: 0.5;
  pointer-events: none;
}

// Gradient Text Effect
.gradient-text {
  background: linear-gradient(to right, #2563eb, #4f46e5);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

// Responsive Design
@media (max-width: 640px) {
  .hero-characters .supporting-characters {
    display: none;
  }

  .cta-characters {
    display: none;
  }

  .demo-questions {
    grid-template-columns: 1fr;
  }

  .demo-question {
    flex-direction: column;
    text-align: center;
  }
}
