// AI Assistant Page Styles
.ai-assistant-container {
  @apply min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800;
}

// Hero Section
.hero-section {
  @apply py-20 px-4 text-center;
}

.hero-content {
  @apply max-w-4xl mx-auto;
}

.hero-icon {
  @apply mb-8 flex justify-center;
  
  .ai-icon {
    @apply w-20 h-20 text-primary-100 dark:text-blue-400;
  }
}

.hero-title {
  @apply text-5xl font-bold text-gray-900 dark:text-white mb-6;
  
  @screen sm {
    @apply text-6xl;
  }
}

.hero-subtitle {
  @apply text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-2xl mx-auto leading-relaxed;
}

.cta-button {
  @apply inline-flex items-center gap-3 px-8 py-4 bg-primary-100 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  
  .button-icon {
    @apply w-5 h-5;
  }
}

// Features Section
.features-section {
  @apply py-20 px-4 bg-white dark:bg-gray-800;
}

.features-container {
  @apply max-w-6xl mx-auto;
}

.section-title {
  @apply text-4xl font-bold text-center text-gray-900 dark:text-white mb-16;
}

.features-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

.feature-card {
  @apply bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2;
}

.feature-icon {
  @apply mb-6 flex justify-center;
  
  svg {
    @apply w-12 h-12 text-primary-100 dark:text-blue-400;
  }
}

.feature-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-4;
}

.feature-description {
  @apply text-gray-600 dark:text-gray-300 leading-relaxed;
}

// How to Use Section
.how-to-use-section {
  @apply py-20 px-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800;
}

.how-to-container {
  @apply max-w-5xl mx-auto;
}

.steps-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-8;
}

.step-card {
  @apply bg-white dark:bg-gray-700 p-8 rounded-2xl text-center shadow-md hover:shadow-lg transition-all duration-300;
}

.step-number {
  @apply w-12 h-12 bg-primary-100 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6;
}

.step-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-4;
}

.step-description {
  @apply text-gray-600 dark:text-gray-300 leading-relaxed;
}


.question-icon {
  @apply mb-4 flex justify-start;

  svg {
    @apply w-6 h-6 text-primary-100 dark:text-blue-400;
  }
}

.question-text {
  @apply text-gray-700 dark:text-gray-300 font-medium;
}


// Responsive Design
@screen sm {
  .hero-title {
    @apply text-5xl;
  }
  
  .features-grid {
    @apply grid-cols-2;
  }
}

@screen lg {
  .features-grid {
    @apply grid-cols-4;
  }
  
  .steps-grid {
    @apply grid-cols-3;
  }
}

// Dark Mode Enhancements
@media (prefers-color-scheme: dark) {
  .ai-assistant-container {
    @apply from-gray-900 to-gray-800;
  }
  
  .features-section {
    @apply bg-gray-800;
  }
  
  .how-to-use-section {
    @apply from-gray-900 to-gray-800;
  }
}

// Animation Classes
.fade-in {
  @apply opacity-0 translate-y-4;
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    @apply opacity-100 translate-y-0;
  }
}

@keyframes pulse {
  0%, 100% {
    @apply opacity-100;
  }
  50% {
    @apply opacity-75;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    @apply transform translate-y-0;
  }
  40%, 43% {
    @apply transform -translate-y-2;
  }
  70% {
    @apply transform -translate-y-1;
  }
  90% {
    @apply transform -translate-y-0.5;
  }
}

// Hover Effects
.feature-card:hover {
  @apply transform -translate-y-2 shadow-xl;
  animation: bounce 0.6s ease-in-out;
}

.step-card:hover {
  @apply transform -translate-y-1 shadow-lg;
}



// Loading State
.loading {
  @apply opacity-50 pointer-events-none;
}

// Gradient Text Effect
.gradient-text {
  @apply bg-gradient-to-r from-primary-100 to-primary-50 bg-clip-text text-transparent;
}

// Floating Animation for Hero Icon
.hero-icon .ai-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    @apply transform translate-y-0;
  }
  50% {
    @apply transform -translate-y-2;
  }
}
