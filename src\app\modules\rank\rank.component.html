<div
  app-breadcrumb
  class="z-10 mt-2 mb-5 md:container mx-auto flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Xếp hạng', url: '/xep-hang' }
  ]"
></div>

<div id="content" class="md:container mx-auto mt-5 dark:text-white">
  <!-- Enhanced Ranking Container -->
  <!-- Subtle Decorative Elements -->
  <div class="ranking-decoration star opacity-5">⭐</div>

  <!-- Enhanced Header Section -->
  <div class="ranking-header">
    <div>
      <h1 class="ranking-title">Bảng Xếp Hạng Truyện Tranh</h1>
      <p class="text-neutral-600 dark:text-neutral-400 mt-2">
        Khám phá những bộ truyện tranh được yêu thích nhất
      </p>
    </div>

    <!-- Enhanced Filter Controls -->
    <div class="ranking-filters">
      <div class="filter-group">
        <label class="filter-label">Sắp xếp theo</label>
        <div
          app-selection-2
          class="filter-select"
          [options]="dataView.sorts"
          (selectedValueChange)="onSortOptionChange($event)"
          [selectedValue]="selectOptions.sorts.value"
        >
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">Trạng thái</label>
        <div
          app-selection-2
          class="filter-select"
          [options]="dataView.status"
          (selectedValueChange)="onStatusOptionChange($event)"
          [selectedValue]="selectOptions.status.value"
        >
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Ranking List Section -->
  <div
    app-grid-comic
    id="listComic"
    [listComics]="listComics | slice : 0 : listComics.length"
  ></div>
  <div
    app-pagination
    *ngIf="totalpage > 1 && isBrowser"
    [totalpage]="totalpage"
    [currentPage]="currentPage"
    (OnChange)="onChangePage($event)"
  ></div>
</div>
