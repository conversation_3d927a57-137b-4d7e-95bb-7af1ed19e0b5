import { isPlatformServer } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { Comic, ComicStatus, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import {
  IFilters,
  rankFiltersOptions,
} from '../../components/utils/constants';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';

@Component({
  selector: 'app-rank',
  templateUrl: './rank.component.html',
  styleUrl: './rank.component.scss',
  standalone: false
})
export class RankComponent extends OptimizedBaseComponent implements OnInit {
  listComics: Comic[] = [];
  // listTopComics!: Comic[];

  totalpage!: number;
  totalResult!: number;
  currentPage = 1;
  dataView!: IFilters;

  selectedComic?: Comic;
  isShowDetails = false;

  selectOptions: any = {
    sorts: { value: 8, name: 'Top All' },
    status: { value: -1, name: 'Tất cả' },
  };
  private isOnInit = false;

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID)  override platformId: object,
  ) {
    super(cd, platformId);
    this.dataView = {
      status: rankFiltersOptions.status,
      sorts: rankFiltersOptions.sorts,
    };
    this.setupSeo();
  }


  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const page = Number(params['page']) || 1;
      const status =
        Number(params['status']) >= 0
          ? Number(params['status'])
          : ComicStatus.ALL;
      const sort =
        Number(params['sort']) >= 0 ? Number(params['sort']) : SortType.TopAll;

      this.currentPage = page;
      this.onSearchComic(page, sort, status);
    });
  }


  onSearchComic(page: number, sort: number, status: number) {
    this.comicService
      .getComics({
        step: '35',
        genre: '-1',
        page: page.toString(),
        sort: sort.toString(),
        status: status.toString(),
      })
      .subscribe((res: any) => {
        this.totalpage = res.data.totalpage;
        this.listComics = res.data.comics;

        // Show details of the first comic if not already selected
        if (!this.selectedComic && this.listComics.length > 0) {
          this.showDetails(this.listComics[0]);
        }
        // this.getTopComics();
        this.safeMarkForCheck();
      });
  }

  // getTopComics() {
  //   if (!this.listComics) return;
  //   this.listTopComics = this.listComics.slice(0, 5);
  // }

  onSortOptionChange(value: number) {
    this.selectOptions.sorts.value = value;
    this.router.navigate([], {
      queryParams: {
        sort: this.selectOptions.sorts.value,
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }
  onStatusOptionChange(value: number) {
    this.selectOptions.status.value = value;
    this.router.navigate([], {
      queryParams: {
        status: this.selectOptions.status.value,
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }


  showDetails(comic: Comic) {
    this.selectedComic = comic;
  }

  onChangePage(page: number) {
    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }
  setupSeo() {
    this.seoService.setTitle('Bảng xếp hạng truyện tranh - MeTruyenMoi');
    this.seoService.addTags([
      {
        name: 'description',
        content:
          'Bảng xếp hạng - Xếp hạng tất cả truyện đều có thể tìm thấy tại MeTruyenMoi- MeTruyenMoi',
      },
      {
        property: 'og:title',
        content: 'Bảng xếp hạng truyện tranh - MeTruyenMoi',
      },
      {
        property: 'og:description',
        content:
          'Bảng xếp hạng - Xếp hạng tất cả truyện đều có thể tìm thấy tại MeTruyenMoi - MeTruyenMoi',
      },

    ]);
    this.seoService.updateLink('canonical', `${this.urlService.baseUrl}/xep-hang`); // Update the canonical link;
  }
}
