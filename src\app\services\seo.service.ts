import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Chapter, Comic, Genre } from '@schema';
import { UrlService } from './url.service';

export interface SEOData {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'book' | 'video';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  private readonly siteName = 'MeTruyenMoi';
  private readonly defaultImage = 'favicon.png/';
  private readonly themeColor = '#fff';

  constructor(
    private title: Title,
    private meta: Meta,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: object
  ) {
  }

  /**
   * Clear all dynamic meta tags before setting new ones
   */
  private clearDynamicMetaTags(): void {
    // Remove dynamic meta tags that should be updated per page
    const dynamicTags = [
      'description', 'author', 'robots',
      'og:title', 'og:description', 'og:type', 'og:url', 'og:image',
      'twitter:title', 'twitter:description', 'twitter:url', 'twitter:image', 'twitter:card',
      'article:published_time', 'article:modified_time', 'article:section', 'article:tag'
    ];

    dynamicTags.forEach(tag => {
      if (tag.startsWith('og:') || tag.startsWith('twitter:') || tag.startsWith('article:')) {
        this.meta.removeTag(`property="${tag}"`);
      } else {
        this.meta.removeTag(`name="${tag}"`);
      }
    });
  }

  /**
   * Set comprehensive SEO data for a page
   */
  setSEOData(data: SEOData): void {
    // Clear existing dynamic meta tags first
    this.clearDynamicMetaTags();

    // Set title
    const fullTitle = data.title ? `${data.title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);

    // Get current URL
    const currentUrl = data.url || `${this.urlService.baseUrl}${this.router.url}`;

    // Basic meta tags
    this.updateTag({ name: 'description', content: data.description });
    this.updateTag({ name: 'author', content: data.author || this.siteName });

    // Robots meta
    const robotsContent = this.getRobotsContent(data.noindex, data.nofollow);
    this.updateTag({ name: 'robots', content: robotsContent });

    // Open Graph tags
    this.updateTag({ property: 'og:title', content: data.title || this.siteName });
    this.updateTag({ property: 'og:description', content: data.description });
    this.updateTag({ property: 'og:type', content: data.type || 'website' });
    this.updateTag({ property: 'og:url', content: currentUrl });
    this.updateTag({ property: 'og:image', content: data.image || `${this.urlService.baseUrl}${this.defaultImage}` });
    this.updateTag({ property: 'og:site_name', content: data.siteName || this.siteName });
    this.updateTag({ property: 'og:locale', content: data.locale || 'vi_VN' });

    // Article specific tags
    if (data.type === 'article' || data.type === 'book') {
      if (data.publishedTime) {
        this.updateTag({ property: 'article:published_time', content: data.publishedTime });
      }
      if (data.modifiedTime) {
        this.updateTag({ property: 'article:modified_time', content: data.modifiedTime });
      }
      if (data.section) {
        this.updateTag({ property: 'article:section', content: data.section });
      }
      if (data.tags) {
        data.tags.forEach(tag => {
          this.addTag({ property: 'article:tag', content: tag });
        });
      }
    }

    // Twitter Card tags
    this.updateTag({ name: 'twitter:card', content: data.twitterCard || 'summary_large_image' });
    this.updateTag({ name: 'twitter:title', content: data.title || this.siteName });
    this.updateTag({ name: 'twitter:description', content: data.description });
    this.updateTag({ name: 'twitter:image', content: data.image || `${this.urlService.baseUrl}${this.defaultImage}` });

    // Canonical URL
    this.updateCanonical(data.canonical || currentUrl);

    // Additional meta tags for better SEO (using consistent theme color)
    this.updateTag({ name: 'theme-color', content: this.themeColor });
    this.updateTag({ name: 'msapplication-TileColor', content: this.themeColor });
  }

  /**
   * Add structured data (JSON-LD)
   */
  addStructuredData(schema: any): void {
    if (isPlatformBrowser(this.platformId)) {
      // Remove existing structured data
      this.removeAllStructuredData();

      // Handle both single schema and array of schemas
      const schemas = Array.isArray(schema) ? schema : [schema];

      schemas.forEach((singleSchema, index) => {
        const script = this.document.createElement('script');
        script.type = 'application/ld+json';
        script.text = JSON.stringify(singleSchema, null, 0); // Minified JSON
        script.id = `structured-data-${index}`;
        script.setAttribute('data-schema-type', singleSchema['@type'] || 'Unknown');
        this.document.head.appendChild(script);
      });
    }
  }

  /**
   * Remove all existing structured data
   */
  private removeAllStructuredData(): void {
    // Remove all structured data scripts
    const existingScripts = this.document.querySelectorAll('script[type="application/ld+json"]');
    existingScripts.forEach(script => {
      if (script.id.startsWith('structured-data-') || script.id === 'structured-data') {
        script.remove();
      }
    });
  }

  /**
   * Set page title
   */
  setTitle(title: string): void {
    const fullTitle = title ? `${title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);
  }

  /**
   * Add meta tags
   */
  addTags(tags: MetaDefinition[]): void {
    this.meta.addTags(tags);
  }

  /**
   * Add single meta tag
   */
  addTag(tag: MetaDefinition): void {
    this.meta.addTag(tag);
  }

  /**
   * Update meta tag
   */
  updateTag(tag: MetaDefinition): void {
    this.meta.updateTag(tag);
  }

  /**
   * Update canonical link
   */
  updateCanonical(url: string): void {
    this.updateLink('canonical', url);
  }

  /**
   * Update link element
   */
  updateLink(rel: string, href: string): void {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(`link[rel='${rel}']`);

    if (!element) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }

    element.setAttribute('rel', rel);
    element.setAttribute('href', href);
  }

  /**
   * Get default keywords
   */
  private getDefaultKeywords(): string {
    return 'truyện tranh, manga, comic, đọc truyện online, truyện tranh hay, manga việt nam, comic việt nam, truyện tranh miễn phí';
  }

  /**
   * Generate consistent author object for E-A-T signals
   */
  generateAuthorObject(authorName?: string): any {
    if (authorName && authorName.trim()) {
      return {
        '@type': 'Person',
        'name': authorName.trim()
      };
    }
    return undefined;
    // Use organization as author for better E-A-T signals when no author is available
  }

  /**
   * Get robots content
   */
  private getRobotsContent(noindex?: boolean, nofollow?: boolean): string {
    const index = noindex ? 'noindex' : 'index';
    const follow = nofollow ? 'nofollow' : 'follow';
    return `${index}, ${follow}`;
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbSchema(breadcrumbs: Array<{ name: string, url: string }>): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': `${this.urlService.baseUrl}${item.url}`
      }))
    };
  }

  /**
   * Generate website structured data
   */
  generateWebsiteSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': this.siteName,
      'url': this.urlService.baseUrl,
      'description': 'Website đọc truyện tranh online miễn phí với kho tàng truyện tranh phong phú, cập nhật liên tục',
      'potentialAction': {
        '@type': 'SearchAction',
        'target': `${this.urlService.baseUrl}/tim-kiem?keyword={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.urlService.baseUrl}/logo.png`,
          'width': 339,
          'height': 160
        }
      }
    };
  }

  /**
   * Generate comic book structured data optimized for manga/comic websites
   */
  generateComicSchema(comic: Comic): any {
    const schema: any = {
      '@context': 'https://schema.org',
      '@type': 'Book',
      'name': comic.title,
      'alternateName': comic.otherName || undefined,
      'description': comic.description || `Đọc truyện ${comic.title} online miễn phí tại ${this.siteName}`,
      'image': comic.coverImage || `${this.urlService.baseUrl}${this.defaultImage}`,
      'url': `${this.urlService.baseUrl}/truyen-tranh/${comic.url}-${comic.id}`,
      'identifier': {
        '@type': 'PropertyValue',
        'name': 'Comic ID',
        'value': comic.id.toString()
      },
      'author': this.generateAuthorObject(comic.author),
      'genre': comic.genres?.map((g) => g.title) || [],
      'bookFormat': 'GraphicNovel',
      'inLanguage': 'vi',
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.baseUrl
      },
      'datePublished': comic.createAt || comic.updateAt,
      'dateModified': comic.updateAt,
      'numberOfPages': comic.numChapter,
      'workExample': comic.chapters?.slice(0, 5).map((chapter: Chapter) => ({
        '@type': 'Chapter',
        'name': chapter.title || `Chương ${chapter.slug}`,
        'url': `${this.urlService.baseUrl}/truyen-tranh/${comic.url}/${chapter.id}`,
        'position': chapter.slug || chapter.id
      })) || [],
      'audience': {
        '@type': 'Audience',
        'audienceType': 'Comic Readers'
      },
      'contentRating': this.getContentRating(comic.genres),
      'keywords': this.generateComicKeywords(comic)
    };

    return schema;
  }

  /**
   * Generate content rating based on genres
   */
  private getContentRating(genres: Genre[]): string {
    if (!genres) return 'General';
    const genreNames = genres.map(g => (g.title).toLowerCase());
    if (genreNames.some(name => ['adult', 'Smut', 'mature', 'ecchi', '18+'].includes(name))) {
      return 'Mature';
    }
    if (genreNames.some(name => ['romance', 'drama', 'psychological'].includes(name))) {
      return 'Teen';
    }
    return 'General';
  }

  /**
   * Generate keywords for comic
   */
  private generateComicKeywords(comic: Comic): string {
    const keywords = [
      comic.title,
      `truyện ${comic.title}`,
      'manga', 'manhwa', 'manhua',
      'truyện tranh online',
      'đọc truyện miễn phí'
    ];

    if (comic.genres) {
      keywords.push(...comic.genres.map((g) => g.title));
    }

    if (comic.author) {
      keywords.push(comic.author);
    }

    return keywords.filter(Boolean).join(', ');
  }

  /**
   * Generate article structured data for chapters optimized for comic reading
   */
  generateChapterSchema(comic: Comic, chapter: Chapter): any {
    const chapterName = `Chương ${chapter.slug}`;
    const chapterUrl = `${this.urlService.baseUrl}/truyen-tranh/${comic.url}/${chapter.id}`;

    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': `${comic.title} ${chapterName}`,
      'alternativeHeadline': `Đọc ${comic.title} ${chapterName} Online Miễn Phí`,
      'description': `Đọc ${chapterName} của truyện ${comic.title} online miễn phí tại ${this.siteName}. Chất lượng cao, tải nhanh.`,
      'image': comic.coverImage || `${this.urlService.baseUrl}${this.defaultImage}`,
      'url': chapterUrl,
      'datePublished': chapter.updateAt,
      'dateModified': chapter.updateAt,
      'author': this.generateAuthorObject(comic.author),
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.urlService.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.urlService.baseUrl}/logo.png`,
          'width': 339,
          'height': 160
        }
      },
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': chapterUrl
      },
      'isPartOf': {
        '@type': 'Book',
        'name': comic.title,
        'url': `${this.urlService.baseUrl}/truyen-tranh/${comic.url}-${comic.id}`,
        'author': this.generateAuthorObject(comic.author)
      },
      'genre': comic.genres?.map((g) => g.title) || [],
      'inLanguage': 'vi',
      'keywords': `${comic.title}, ${chapterName}, truyện tranh online, đọc truyện miễn phí, manga, manhwa`,
      'articleSection': 'Truyện Tranh',
      'pagination': chapter.slug || chapter.id,
      'position': chapter.slug || chapter.id,
      'audience': {
        '@type': 'Audience',
        'audienceType': 'Comic Readers'
      }
    };
  }

  /**
   * Generate ItemList schema for comic listings (homepage, genre pages, etc.)
   */
  generateComicListSchema(comics: Comic[], listName: string, listDescription?: string): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      'name': listName,
      'description': listDescription || `Danh sách ${listName.toLowerCase()} tại ${this.siteName}`,
      'numberOfItems': comics.length,
      'itemListOrder': 'https://schema.org/ItemListOrderDescending',
      'itemListElement': comics.map((comic, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'item': {
          '@type': 'Book',
          'name': comic.title,
          'url': `${this.urlService.baseUrl}/truyen-tranh/${comic.url}-${comic.id}`,
          'image': comic.coverImage,
          'author': this.generateAuthorObject(comic.author),
          'genre': comic.genres?.map((g) => g.title) || [],
          'dateModified': comic.updateAt,
        }
      }))
    };
  }

  /**
   * Generate FAQ schema for comic pages
   */
  generateComicFAQSchema(comic: Comic): any {
    const latestChapter = comic.chapters?.[0]?.slug;
    const status = comic.status === 0 ? 'Đang cập nhật' : 'Hoàn thành';
    const totalChapters = comic.numChapter;

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': [
        {
          '@type': 'Question',
          'name': `${comic.title} có bao nhiêu chương?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${comic.title} hiện tại có ${totalChapters} chương, chương mới nhất là chương ${latestChapter}.`
          }
        },
        {
          '@type': 'Question',
          'name': `Tình trạng của ${comic.title} như thế nào?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${comic.title} hiện tại đang ở trạng thái ${status}.`
          }
        },
        {
          '@type': 'Question',
          'name': `Đọc ${comic.title} ở đâu miễn phí?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `Bạn có thể đọc ${comic.title} miễn phí tại ${this.siteName} với chất lượng hình ảnh cao và cập nhật nhanh nhất.`
          }
        },
        {
          '@type': 'Question',
          'name': `${comic.title} thuộc thể loại gì?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${comic.title} thuộc thể loại ${comic.genres?.map((g) => g.title).join(', ') || 'Đang cập nhật'}.`
          }
        }
      ]
    };
  }

  /**
   * Generate Organization schema for the website
   */
  generateOrganizationSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': this.siteName,
      'url': this.urlService.baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${this.urlService.baseUrl}/logo.png`,
        'width': 339,
        'height': 160
      },
      'description': 'Website đọc truyện tranh online hàng đầu Việt Nam với kho tàng manga, manhwa, manhua phong phú',
      'foundingDate': '2024',
      'contactPoint': {
        '@type': 'ContactPoint',
        'contactType': 'customer service',
        'availableLanguage': 'Vietnamese'
      },
      'sameAs': [
        'https://www.facebook.com/metruyenmoicom',
      ],
      'areaServed': 'VN',
      'serviceType': 'Online Comic Reading Platform'
    };
  }
}
