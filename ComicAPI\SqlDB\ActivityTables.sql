-- Activity Tracking Tables for ComicAPI
-- These tables store user behavior and activity data for analytics and personalization

-- User Activities Table - General activity tracking
CREATE TABLE user_activities (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    activity_type INTEGER NOT NULL,
    entity_type VARCHAR(50),
    entity_id INTEGER,
    metadata TEXT, -- JSO<PERSON> data for additional information
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES _USER(ID) ON DELETE CASCADE
);

-- User Reading History Table - Detailed reading tracking
CREATE TABLE user_reading_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    comic_id INTEGER NOT NULL,
    chapter_id INTEGER NOT NULL,
    page_number INTEGER,
    reading_progress FLOAT DEFAULT 0, -- 0-100%
    reading_time_seconds INTEGER DEFAULT 0,
    first_read_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    read_count INTEGER DEFAULT 1,
    is_completed BOOLEAN DEFAULT FALSE,
    
    FOREIGN KEY (user_id) REFERENCES _USER(ID) ON DELETE CASCADE,
    FOREIGN KEY (comic_id) REFERENCES COMIC(ID) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES CHAPTER(ID) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate entries
    UNIQUE(user_id, comic_id, chapter_id)
);

-- User Login History Table - Login/logout tracking
CREATE TABLE user_login_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    login_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    logout_at TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_info TEXT, -- JSON data about device
    location VARCHAR(255), -- City, Country
    session_duration_seconds INTEGER,
    is_successful BOOLEAN DEFAULT TRUE,
    failure_reason VARCHAR(255),
    
    FOREIGN KEY (user_id) REFERENCES _USER(ID) ON DELETE CASCADE
);

-- User Session Activity Table - Session-based tracking
CREATE TABLE user_session_activities (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP,
    total_duration_seconds INTEGER DEFAULT 0,
    pages_visited INTEGER DEFAULT 0,
    comics_viewed INTEGER DEFAULT 0,
    chapters_read INTEGER DEFAULT 0,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (user_id) REFERENCES _USER(ID) ON DELETE CASCADE,
    
    -- Unique constraint for session tracking
    UNIQUE(user_id, session_id)
);

-- Indexes for performance optimization
CREATE INDEX idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX idx_user_activities_created_at ON user_activities(created_at);
CREATE INDEX idx_user_activities_activity_type ON user_activities(activity_type);
CREATE INDEX idx_user_activities_entity ON user_activities(entity_type, entity_id);

CREATE INDEX idx_user_reading_history_user_id ON user_reading_history(user_id);
CREATE INDEX idx_user_reading_history_comic_id ON user_reading_history(comic_id);
CREATE INDEX idx_user_reading_history_last_read ON user_reading_history(last_read_at);
CREATE INDEX idx_user_reading_history_user_comic ON user_reading_history(user_id, comic_id);

CREATE INDEX idx_user_login_history_user_id ON user_login_history(user_id);
CREATE INDEX idx_user_login_history_login_at ON user_login_history(login_at);
CREATE INDEX idx_user_login_history_ip ON user_login_history(ip_address);

CREATE INDEX idx_user_session_activities_user_id ON user_session_activities(user_id);
CREATE INDEX idx_user_session_activities_session_id ON user_session_activities(session_id);
CREATE INDEX idx_user_session_activities_started_at ON user_session_activities(started_at);
CREATE INDEX idx_user_session_activities_is_active ON user_session_activities(is_active);

-- Add last_activity column to _USER table if not exists
ALTER TABLE _USER ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP;
ALTER TABLE _USER ADD COLUMN IF NOT EXISTS last_login_ip VARCHAR(50);

-- Update last_activity trigger
CREATE OR REPLACE FUNCTION update_user_last_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE _USER 
    SET last_activity = CURRENT_TIMESTAMP 
    WHERE ID = NEW.user_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for user activities
DROP TRIGGER IF EXISTS trigger_update_user_last_activity ON user_activities;
CREATE TRIGGER trigger_update_user_last_activity
    AFTER INSERT ON user_activities
    FOR EACH ROW
    EXECUTE FUNCTION update_user_last_activity();

-- Create trigger for reading history
DROP TRIGGER IF EXISTS trigger_update_user_last_activity_reading ON user_reading_history;
CREATE TRIGGER trigger_update_user_last_activity_reading
    AFTER INSERT OR UPDATE ON user_reading_history
    FOR EACH ROW
    EXECUTE FUNCTION update_user_last_activity();

-- Partitioning for large tables (optional, for high-volume applications)
-- CREATE TABLE user_activities_2024 PARTITION OF user_activities
-- FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- Views for common queries
CREATE OR REPLACE VIEW user_activity_summary AS
SELECT 
    u.ID as user_id,
    u.Email,
    u.FirstName,
    u.LastName,
    u.last_activity,
    COUNT(DISTINCT urh.comic_id) as total_comics_read,
    COUNT(urh.id) as total_chapters_read,
    SUM(urh.reading_time_seconds) as total_reading_time_seconds,
    COUNT(DISTINCT DATE(urh.last_read_at)) as reading_days,
    MAX(urh.last_read_at) as last_reading_activity
FROM _USER u
LEFT JOIN user_reading_history urh ON u.ID = urh.user_id
GROUP BY u.ID, u.Email, u.FirstName, u.LastName, u.last_activity;

-- View for recent user activities
CREATE OR REPLACE VIEW recent_user_activities AS
SELECT 
    ua.id,
    ua.user_id,
    u.Email,
    u.FirstName,
    u.LastName,
    ua.activity_type,
    ua.entity_type,
    ua.entity_id,
    ua.created_at,
    ua.ip_address
FROM user_activities ua
JOIN _USER u ON ua.user_id = u.ID
WHERE ua.created_at >= CURRENT_TIMESTAMP - INTERVAL '30 days'
ORDER BY ua.created_at DESC;
