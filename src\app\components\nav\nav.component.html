<header class="nav-header">
  <div class="nav-container">
    <a [routerLink]="['/']" class="nav-logo">
      <img class="w-full h-auto aspect-auto" loading="eager" src="/logo.png" alt="logo" />
    </a>

    <div *ngIf="isBrowser" class="nav-actions">
      <div app-list-search-comic></div>

      <div class="theme-toggle">
        <input
          aria-label="Toggle theme"
          id="theme-toggle"
          (change)="toggleTheme()"
          type="checkbox"
          class="theme-input"
        />
        <span [class.active]="isDarkMode()" class="theme-slider">
          <svg class="theme-icon sun" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 12.667A4.667 4.667 0 1 0 8 3.333a4.667 4.667 0 0 0 0 9.334z" />
            <path
              d="M8 15.307a.667.667 0 0 1-.667-.667v-.053a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.72zm4.76-1.88a.667.667 0 0 1-.473-.194l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.134zm-9.52 0a.667.667 0 0 1-.473-1.134l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.194zM14.667 8.667h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm-13.28 0h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm11.286-4.674a.667.667 0 0 1-.473-.193.667.667 0 0 1 0-.94l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.193zm-9.346 0a.667.667 0 0 1-.473-.193l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.133zM8 2.027a.667.667 0 0 1-.667-.667V1.333a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.694z"
            />
          </svg>
          <svg class="theme-icon moon" viewBox="0 0 16 16" fill="currentColor">
            <path
              d="M14.353 10.62c-.107-.18-.407-.46-1.153-.327-.414.073-.834.107-1.254.087-1.553-.067-2.96-.78-3.94-1.88-.866-.967-1.4-2.227-1.406-3.587 0-.76.146-1.493.446-2.187.294-.673.087-1.027-.06-1.173-.153-.154-.513-.367-1.22-.067C3.04 2.627 1.353 5.36 1.553 8.287c.2 2.76 2.133 6.113 4.693 7 .614.213 1.26.34 1.927.367.067.006.174.013.28.013 2.234 0 4.327-1.053 5.647-2.847.447-.62.327-1.013.213-1.193z"
            />
          </svg>
        </span>
      </div>

      <div app-notify></div>

      <div app-user-menu></div>
    </div>
  </div>
</header>

<nav class="nav-main">
  <div class="nav-mobile">
    <div class="nav-home">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path
          d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
        />
      </svg>
      <a [routerLink]="['/']">Trang chủ</a>
    </div>
    <button type="button" aria-label="Toggle menu" class="nav-toggle" (click)="toggleSidebar()">
      <svg
        *ngIf="!showSidebar"
        class="nav-icon"
        viewBox="0 0 24 24"
        stroke="currentColor"
        fill="none"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16M4 18h7"
        />
      </svg>
      <svg
        *ngIf="showSidebar"
        class="nav-icon"
        viewBox="0 0 24 24"
        stroke="currentColor"
        fill="none"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </button>
  </div>

  <ul class="nav-desktop">
    <li>
      <a routerLink="/" aria-label="Trang chủ" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
          />
        </svg>
      </a>
    </li>

    <li>
      <a [routerLink]="['/truyen-hot']" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
          />
        </svg>
        Hot
      </a>
    </li>

    <li class="nav-dropdown" (mouseenter)="isShowGenre = true" (mouseleave)="isShowGenre = false">
      <div class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
          />
        </svg>
        Thể loại
      </div>
      <div *ngIf="isShowGenre && isBrowser" class="nav-dropdown-menu">
        <div app-genre-catagories></div>
      </div>
    </li>

    <li>
      <a [routerLink]="['/theo-doi']" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
        Theo dõi
      </a>
    </li>

    <li>
      <a [routerLink]="['/xep-hang']" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
        Xếp hạng
      </a>
    </li>

    <li>
      <a routerLink="/tim-truyen" class="nav-link">
        <svg class="nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
        Tìm kiếm nâng cao
      </a>
    </li>

    <li>
      <a routerLink="lich-su" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        Lịch sử
      </a>
    </li>

    <li>
      <a [routerLink]="['/tro-ly-ai']" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
          />
        </svg>
        Trợ lý AI
      </a>
    </li>

    <li>
      <span (click)="onFeedbackClick()" class="nav-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        Góp ý
      </span>
    </li>
  </ul>

  <ul class="nav-mobile-menu" [class.open]="showSidebar" [class.closed]="!showSidebar">
    <li>
      <a [routerLink]="['/truyen-hot']" class="nav-mobile-link" (click)="showSidebar = false">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
          />
        </svg>
        Hot
      </a>
    </li>

    <li
      class="nav-mobile-dropdown"
      (mouseenter)="isShowGenre = true"
      (mouseleave)="isShowGenre = false"
    >
      <div class="nav-mobile-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
          />
        </svg>
        Thể loại
      </div>
      <div #dropdownMenu *ngIf="isShowGenre && isBrowser" class="nav-mobile-dropdown-menu">
        <div app-genre-catagories></div>
      </div>
    </li>

    <li>
      <a [routerLink]="['/theo-doi']" class="nav-mobile-link" (click)="showSidebar = false">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
        Theo dõi
      </a>
    </li>

    <li>
      <a [routerLink]="['/xep-hang']" class="nav-mobile-link" (click)="showSidebar = false">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
        Xếp hạng
      </a>
    </li>

    <li>
      <a [routerLink]="['/tim-truyen']" class="nav-mobile-link" (click)="showSidebar = false">
        <svg class="nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
        Tìm kiếm nâng cao
      </a>
    </li>

    <li>
      <a [routerLink]="['/lich-su']" class="nav-mobile-link" (click)="showSidebar = false">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        Lịch sử
      </a>
    </li>

    <li>
      <a [routerLink]="['/tro-ly-ai']" class="nav-mobile-link" (click)="showSidebar = false">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
          />
        </svg>
        Trợ lý AI
      </a>
    </li>

    <li>
      <span (click)="onFeedbackClick(); showSidebar = false" class="nav-mobile-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        Góp ý
      </span>
    </li>
    <li>
      <span (click)="onSettingClick(); showSidebar = false" class="nav-mobile-link">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="nav-icon">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          ></path>
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          ></path>
        </svg>
        Cài đặt
      </span>
    </li>
  </ul>
</nav>
