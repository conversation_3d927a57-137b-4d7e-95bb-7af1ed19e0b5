using ComicAPI.Data;
using ComicAPI.DTOs;
using ComicAPI.Enums;
using ComicAPI.Models.Activity;
using ComicAPI.Reposibility;
using ComicAPI.Updater;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Concurrent;
using System.Text.Json;

namespace ComicAPI.Services
{
    public class UserActivityService : IUserActivityService
    {
        private readonly ComicDbContext _dbContext;
        private readonly IMemoryCache _cache;
        private readonly IBackgroundTaskQueue _taskQueue;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UserActivityService> _logger;
        
        // In-memory queue for batch processing
        private readonly ConcurrentQueue<UserActivity> _activityQueue = new();
        private readonly ConcurrentQueue<UserReadingHistory> _readingQueue = new();
        private readonly Timer _batchProcessTimer;
        
        private const int BATCH_SIZE = 100;
        private const int BATCH_INTERVAL_SECONDS = 30;
        private const string CACHE_PREFIX = "user_activity_";
        private const int CACHE_DURATION_MINUTES = 10;

        public UserActivityService(
            ComicDbContext dbContext,
            IMemoryCache cache,
            IBackgroundTaskQueue taskQueue,
            IServiceProvider serviceProvider,
            ILogger<UserActivityService> logger)
        {
            _dbContext = dbContext;
            _cache = cache;
            _taskQueue = taskQueue;
            _serviceProvider = serviceProvider;
            _logger = logger;
            
            // Initialize batch processing timer
            _batchProcessTimer = new Timer(ProcessBatchedActivities, null, 
                TimeSpan.FromSeconds(BATCH_INTERVAL_SECONDS), 
                TimeSpan.FromSeconds(BATCH_INTERVAL_SECONDS));
        }

        public async Task TrackActivityAsync(int userId, UserActivityType activityType, 
            string? entityType = null, int? entityId = null, Dictionary<string, object>? metadata = null, 
            ActivityPriority priority = ActivityPriority.Normal)
        {
            try
            {
                var activity = new UserActivity
                {
                    UserId = userId,
                    ActivityType = activityType,
                    EntityType = entityType,
                    EntityId = entityId,
                    Metadata = metadata != null ? JsonSerializer.Serialize(metadata) : null,
                    CreatedAt = DateTime.UtcNow,
                    SessionId = GetCurrentSessionId(userId)
                };

                if (priority == ActivityPriority.Critical || priority == ActivityPriority.High)
                {
                    // Process immediately for high priority activities
                    _dbContext.UserActivities.Add(activity);
                    await _dbContext.SaveChangesAsync();
                }
                else
                {
                    // Queue for batch processing
                    _activityQueue.Enqueue(activity);
                }

                // Update user last activity in background
                UpdateUserLastActivityBackground(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking activity for user {UserId}, activity {ActivityType}", 
                    userId, activityType);
            }
        }

        public void TrackActivityBackground(int userId, UserActivityType activityType, 
            string? entityType = null, int? entityId = null, Dictionary<string, object>? metadata = null, 
            ActivityPriority priority = ActivityPriority.Normal)
        {
            _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
            {
                using var scope = _serviceProvider.CreateScope();
                var activityService = scope.ServiceProvider.GetRequiredService<IUserActivityService>();
                await activityService.TrackActivityAsync(userId, activityType, entityType, entityId, metadata, priority);
            });
        }

        public async Task TrackReadingProgressAsync(int userId, int comicId, int chapterId, 
            int? pageNumber = null, float readingProgress = 0, int readingTimeSeconds = 0, 
            bool isCompleted = false)
        {
            try
            {
                var existingHistory = await _dbContext.UserReadingHistories
                    .FirstOrDefaultAsync(h => h.UserId == userId && h.ComicId == comicId && h.ChapterId == chapterId);

                if (existingHistory != null)
                {
                    // Update existing record
                    existingHistory.PageNumber = pageNumber ?? existingHistory.PageNumber;
                    existingHistory.ReadingProgress = Math.Max(existingHistory.ReadingProgress, readingProgress);
                    existingHistory.ReadingTimeSeconds += readingTimeSeconds;
                    existingHistory.LastReadAt = DateTime.UtcNow;
                    existingHistory.ReadCount++;
                    existingHistory.IsCompleted = isCompleted || existingHistory.IsCompleted;
                }
                else
                {
                    // Create new record
                    var newHistory = new UserReadingHistory
                    {
                        UserId = userId,
                        ComicId = comicId,
                        ChapterId = chapterId,
                        PageNumber = pageNumber,
                        ReadingProgress = readingProgress,
                        ReadingTimeSeconds = readingTimeSeconds,
                        FirstReadAt = DateTime.UtcNow,
                        LastReadAt = DateTime.UtcNow,
                        ReadCount = 1,
                        IsCompleted = isCompleted
                    };
                    
                    _readingQueue.Enqueue(newHistory);
                }

                // Track activity
                await TrackActivityAsync(userId, UserActivityType.ReadChapter, "Chapter", chapterId, 
                    new Dictionary<string, object>
                    {
                        ["comicId"] = comicId,
                        ["readingProgress"] = readingProgress,
                        ["readingTime"] = readingTimeSeconds,
                        ["isCompleted"] = isCompleted
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking reading progress for user {UserId}, comic {ComicId}, chapter {ChapterId}", 
                    userId, comicId, chapterId);
            }
        }

        public void TrackReadingProgressBackground(int userId, int comicId, int chapterId, 
            int? pageNumber = null, float readingProgress = 0, int readingTimeSeconds = 0, 
            bool isCompleted = false)
        {
            _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
            {
                using var scope = _serviceProvider.CreateScope();
                var activityService = scope.ServiceProvider.GetRequiredService<IUserActivityService>();
                await activityService.TrackReadingProgressAsync(userId, comicId, chapterId, 
                    pageNumber, readingProgress, readingTimeSeconds, isCompleted);
            });
        }

        public async Task TrackLoginAsync(int userId, string? ipAddress = null, string? userAgent = null, 
            string? deviceInfo = null, string? location = null, bool isSuccessful = true, 
            string? failureReason = null)
        {
            try
            {
                var loginHistory = new UserLoginHistory
                {
                    UserId = userId,
                    LoginAt = DateTime.UtcNow,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    DeviceInfo = deviceInfo,
                    Location = location,
                    IsSuccessful = isSuccessful,
                    FailureReason = failureReason
                };

                _dbContext.UserLoginHistories.Add(loginHistory);
                await _dbContext.SaveChangesAsync();

                // Track login activity
                await TrackActivityAsync(userId, UserActivityType.Login, null, null, 
                    new Dictionary<string, object>
                    {
                        ["ipAddress"] = ipAddress ?? "",
                        ["isSuccessful"] = isSuccessful,
                        ["failureReason"] = failureReason ?? ""
                    }, ActivityPriority.High);

                // Update user last login
                var user = await _dbContext.Users.FindAsync(userId);
                if (user != null && isSuccessful)
                {
                    user.LastLogin = DateTime.UtcNow;
                    user.LastLoginIp = ipAddress;
                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking login for user {UserId}", userId);
            }
        }

        public async Task TrackLogoutAsync(int userId, string? sessionId = null)
        {
            try
            {
                // End active session
                if (!string.IsNullOrEmpty(sessionId))
                {
                    await EndSessionAsync(sessionId);
                }

                // Track logout activity
                await TrackActivityAsync(userId, UserActivityType.Logout, null, null, 
                    new Dictionary<string, object>
                    {
                        ["sessionId"] = sessionId ?? ""
                    }, ActivityPriority.High);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking logout for user {UserId}", userId);
            }
        }

        public async Task<string> StartSessionAsync(int userId, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                var sessionId = Guid.NewGuid().ToString();
                
                var sessionActivity = new UserSessionActivity
                {
                    UserId = userId,
                    SessionId = sessionId,
                    StartedAt = DateTime.UtcNow,
                    LastActivityAt = DateTime.UtcNow,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    IsActive = true
                };

                _dbContext.UserSessionActivities.Add(sessionActivity);
                await _dbContext.SaveChangesAsync();

                // Cache session info
                _cache.Set($"{CACHE_PREFIX}session_{sessionId}", sessionActivity, 
                    TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

                return sessionId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting session for user {UserId}", userId);
                return Guid.NewGuid().ToString(); // Return a fallback session ID
            }
        }

        private string? GetCurrentSessionId(int userId)
        {
            // Try to get from cache or create new session
            var cacheKey = $"{CACHE_PREFIX}current_session_{userId}";
            if (_cache.TryGetValue(cacheKey, out string? sessionId))
            {
                return sessionId;
            }
            return null;
        }

        private async void ProcessBatchedActivities(object? state)
        {
            try
            {
                await ProcessPendingActivitiesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing batched activities");
            }
        }

        public async Task ProcessPendingActivitiesAsync()
        {
            var activities = new List<UserActivity>();
            var readingHistories = new List<UserReadingHistory>();

            // Dequeue activities
            while (_activityQueue.TryDequeue(out var activity) && activities.Count < BATCH_SIZE)
            {
                activities.Add(activity);
            }

            // Dequeue reading histories
            while (_readingQueue.TryDequeue(out var reading) && readingHistories.Count < BATCH_SIZE)
            {
                readingHistories.Add(reading);
            }

            if (activities.Any() || readingHistories.Any())
            {
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<ComicDbContext>();

                try
                {
                    if (activities.Any())
                    {
                        dbContext.UserActivities.AddRange(activities);
                    }

                    if (readingHistories.Any())
                    {
                        dbContext.UserReadingHistories.AddRange(readingHistories);
                    }

                    await dbContext.SaveChangesAsync();
                    
                    _logger.LogInformation("Processed {ActivityCount} activities and {ReadingCount} reading histories", 
                        activities.Count, readingHistories.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error saving batched activities to database");
                    
                    // Re-queue failed items
                    foreach (var activity in activities)
                        _activityQueue.Enqueue(activity);
                    foreach (var reading in readingHistories)
                        _readingQueue.Enqueue(reading);
                }
            }
        }

        public async Task UpdateUserLastActivityAsync(int userId)
        {
            try
            {
                var user = await _dbContext.Users.FindAsync(userId);
                if (user != null)
                {
                    user.LastActivity = DateTime.UtcNow;
                    await _dbContext.SaveChangesAsync();
                    
                    // Update cache
                    _cache.Set($"{CACHE_PREFIX}last_activity_{userId}", DateTime.UtcNow, 
                        TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating last activity for user {UserId}", userId);
            }
        }

        public void UpdateUserLastActivityBackground(int userId)
        {
            _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
            {
                using var scope = _serviceProvider.CreateScope();
                var activityService = scope.ServiceProvider.GetRequiredService<IUserActivityService>();
                await activityService.UpdateUserLastActivityAsync(userId);
            });
        }

        public async Task<List<UserActivityDTO>> GetUserActivitiesAsync(int userId, int page = 1, int pageSize = 20,
            UserActivityType? activityType = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbContext.UserActivities
                .Where(a => a.UserId == userId);

            if (activityType.HasValue)
                query = query.Where(a => a.ActivityType == activityType.Value);

            if (fromDate.HasValue)
                query = query.Where(a => a.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(a => a.CreatedAt <= toDate.Value);

            var activities = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(a => new UserActivityDTO
                {
                    Id = a.Id,
                    UserId = a.UserId,
                    ActivityType = a.ActivityType,
                    EntityType = a.EntityType,
                    EntityId = a.EntityId,
                    Metadata = a.Metadata,
                    CreatedAt = a.CreatedAt,
                    SessionId = a.SessionId
                })
                .ToListAsync();

            return activities;
        }

        public async Task<List<UserReadingHistoryDTO>> GetUserReadingHistoryAsync(int userId, int page = 1, int pageSize = 20,
            int? comicId = null, bool? isCompleted = null)
        {
            var query = _dbContext.UserReadingHistories
                .Include(h => h.Comic)
                .Include(h => h.Chapter)
                .Where(h => h.UserId == userId);

            if (comicId.HasValue)
                query = query.Where(h => h.ComicId == comicId.Value);

            if (isCompleted.HasValue)
                query = query.Where(h => h.IsCompleted == isCompleted.Value);

            var histories = await query
                .OrderByDescending(h => h.LastReadAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(h => new UserReadingHistoryDTO
                {
                    Id = h.Id,
                    UserId = h.UserId,
                    ComicId = h.ComicId,
                    ComicTitle = h.Comic!.Title,
                    ComicCoverImage = h.Comic.CoverImage ?? "",
                    ChapterId = h.ChapterId,
                    ChapterTitle = h.Chapter!.Title,
                    ChapterUrl = h.Chapter.Url,
                    PageNumber = h.PageNumber,
                    ReadingProgress = h.ReadingProgress,
                    ReadingTimeSeconds = h.ReadingTimeSeconds,
                    FirstReadAt = h.FirstReadAt,
                    LastReadAt = h.LastReadAt,
                    ReadCount = h.ReadCount,
                    IsCompleted = h.IsCompleted
                })
                .ToListAsync();

            return histories;
        }

        public async Task<List<UserLoginHistoryDTO>> GetUserLoginHistoryAsync(int userId, int page = 1, int pageSize = 20,
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbContext.UserLoginHistories
                .Where(h => h.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(h => h.LoginAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(h => h.LoginAt <= toDate.Value);

            var histories = await query
                .OrderByDescending(h => h.LoginAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(h => new UserLoginHistoryDTO
                {
                    Id = h.Id,
                    LoginAt = h.LoginAt,
                    LogoutAt = h.LogoutAt,
                    IpAddress = h.IpAddress,
                    DeviceInfo = h.DeviceInfo,
                    Location = h.Location,
                    SessionDurationSeconds = h.SessionDurationSeconds,
                    IsSuccessful = h.IsSuccessful
                })
                .ToListAsync();

            return histories;
        }

        public async Task<UserActivityStatsDTO> GetUserActivityStatsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var readingHistories = _dbContext.UserReadingHistories
                .Include(h => h.Comic)
                .ThenInclude(c => c.Genres)
                .Where(h => h.UserId == userId);

            if (fromDate.HasValue)
                readingHistories = readingHistories.Where(h => h.LastReadAt >= fromDate.Value);

            if (toDate.HasValue)
                readingHistories = readingHistories.Where(h => h.LastReadAt <= toDate.Value);

            var histories = await readingHistories.ToListAsync();

            var stats = new UserActivityStatsDTO
            {
                TotalComicsRead = histories.Select(h => h.ComicId).Distinct().Count(),
                TotalChaptersRead = histories.Count,
                TotalReadingTimeHours = histories.Sum(h => h.ReadingTimeSeconds) / 3600,
                LastActivity = await GetUserLastActivityAsync(userId)
            };

            // Calculate login days and streaks
            var loginDates = await _dbContext.UserLoginHistories
                .Where(h => h.UserId == userId && h.IsSuccessful)
                .Select(h => h.LoginAt.Date)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            stats.TotalLoginDays = loginDates.Count;
            stats.CurrentStreak = CalculateCurrentStreak(loginDates);
            stats.LongestStreak = CalculateLongestStreak(loginDates);

            return stats;
        }

        public async Task<List<DailyActivityDTO>> GetDailyActivityAsync(int userId, DateTime fromDate, DateTime toDate)
        {
            var activities = new List<DailyActivityDTO>();

            for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
            {
                var nextDate = date.AddDays(1);

                var chaptersRead = await _dbContext.UserReadingHistories
                    .Where(h => h.UserId == userId && h.LastReadAt >= date && h.LastReadAt < nextDate)
                    .CountAsync();

                var readingTime = await _dbContext.UserReadingHistories
                    .Where(h => h.UserId == userId && h.LastReadAt >= date && h.LastReadAt < nextDate)
                    .SumAsync(h => h.ReadingTimeSeconds);

                var loginCount = await _dbContext.UserLoginHistories
                    .Where(h => h.UserId == userId && h.LoginAt >= date && h.LoginAt < nextDate && h.IsSuccessful)
                    .CountAsync();

                activities.Add(new DailyActivityDTO
                {
                    Date = date,
                    ChaptersRead = chaptersRead,
                    ReadingTimeMinutes = readingTime / 60,
                    LoginCount = loginCount,
                    IsActive = chaptersRead > 0 || loginCount > 0
                });
            }

            return activities;
        }

        public async Task<List<GenreStatsDTO>> GetUserGenreStatsAsync(int userId)
        {
            var genreStats = await _dbContext.UserReadingHistories
                .Include(h => h.Comic)
                .ThenInclude(c => c.Genres)
                .Where(h => h.UserId == userId)
                .SelectMany(h => h.Comic!.Genres.Select(g => new { Genre = g, History = h }))
                .GroupBy(x => new { x.Genre.ID, x.Genre.Name })
                .Select(g => new GenreStatsDTO
                {
                    GenreId = g.Key.ID,
                    GenreName = g.Key.Name,
                    ComicsRead = g.Select(x => x.History.ComicId).Distinct().Count(),
                    ChaptersRead = g.Count(),
                    ReadingTimeHours = g.Sum(x => x.History.ReadingTimeSeconds) / 3600
                })
                .OrderByDescending(g => g.ChaptersRead)
                .ToListAsync();

            return genreStats;
        }

        public async Task UpdateSessionActivityAsync(string sessionId, bool incrementPages = false,
            bool incrementComics = false, bool incrementChapters = false)
        {
            try
            {
                var session = await _dbContext.UserSessionActivities
                    .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.IsActive);

                if (session != null)
                {
                    session.LastActivityAt = DateTime.UtcNow;
                    session.TotalDurationSeconds = (int)(DateTime.UtcNow - session.StartedAt).TotalSeconds;

                    if (incrementPages) session.PagesVisited++;
                    if (incrementComics) session.ComicsViewed++;
                    if (incrementChapters) session.ChaptersRead++;

                    await _dbContext.SaveChangesAsync();

                    // Update cache
                    _cache.Set($"{CACHE_PREFIX}session_{sessionId}", session,
                        TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating session activity for session {SessionId}", sessionId);
            }
        }

        public async Task EndSessionAsync(string sessionId)
        {
            try
            {
                var session = await _dbContext.UserSessionActivities
                    .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.IsActive);

                if (session != null)
                {
                    session.EndedAt = DateTime.UtcNow;
                    session.IsActive = false;
                    session.TotalDurationSeconds = (int)(DateTime.UtcNow - session.StartedAt).TotalSeconds;

                    await _dbContext.SaveChangesAsync();

                    // Remove from cache
                    _cache.Remove($"{CACHE_PREFIX}session_{sessionId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ending session {SessionId}", sessionId);
            }
        }

        public async Task CleanupOldActivitiesAsync(int daysToKeep = 365)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);

                // Clean up old activities
                var oldActivities = await _dbContext.UserActivities
                    .Where(a => a.CreatedAt < cutoffDate)
                    .ToListAsync();

                if (oldActivities.Any())
                {
                    _dbContext.UserActivities.RemoveRange(oldActivities);
                    await _dbContext.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} old activities", oldActivities.Count);
                }

                // Clean up old login histories (keep more recent ones)
                var loginCutoffDate = DateTime.UtcNow.AddDays(-180); // Keep 6 months
                var oldLogins = await _dbContext.UserLoginHistories
                    .Where(h => h.LoginAt < loginCutoffDate)
                    .ToListAsync();

                if (oldLogins.Any())
                {
                    _dbContext.UserLoginHistories.RemoveRange(oldLogins);
                    await _dbContext.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} old login histories", oldLogins.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old activities");
            }
        }

        public async Task<bool> IsUserActiveAsync(int userId, TimeSpan timeWindow)
        {
            var cutoffTime = DateTime.UtcNow.Subtract(timeWindow);

            // Check cache first
            var cacheKey = $"{CACHE_PREFIX}last_activity_{userId}";
            if (_cache.TryGetValue(cacheKey, out DateTime lastActivity))
            {
                return lastActivity >= cutoffTime;
            }

            // Check database
            var user = await _dbContext.Users.FindAsync(userId);
            return user?.LastActivity >= cutoffTime;
        }

        public async Task<DateTime?> GetUserLastActivityAsync(int userId)
        {
            // Check cache first
            var cacheKey = $"{CACHE_PREFIX}last_activity_{userId}";
            if (_cache.TryGetValue(cacheKey, out DateTime lastActivity))
            {
                return lastActivity;
            }

            // Check database
            var user = await _dbContext.Users.FindAsync(userId);
            if (user?.LastActivity != null)
            {
                _cache.Set(cacheKey, user.LastActivity.Value, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return user?.LastActivity;
        }

        public async Task<int> GetUserActiveSessionsCountAsync(int userId)
        {
            return await _dbContext.UserSessionActivities
                .CountAsync(s => s.UserId == userId && s.IsActive);
        }

        public async Task<List<MonthlyActivityDTO>> GetMonthlyActivityAsync(int userId, int year)
        {
            var activities = new List<MonthlyActivityDTO>();

            for (int month = 1; month <= 12; month++)
            {
                var startDate = new DateTime(year, month, 1);
                var endDate = startDate.AddMonths(1);

                var monthlyData = await _dbContext.UserReadingHistories
                    .Where(h => h.UserId == userId && h.LastReadAt >= startDate && h.LastReadAt < endDate)
                    .GroupBy(h => 1)
                    .Select(g => new
                    {
                        TotalChapters = g.Count(),
                        TotalReadingTime = g.Sum(h => h.ReadingTimeSeconds),
                        ActiveDays = g.Select(h => h.LastReadAt.Date).Distinct().Count(),
                        NewComics = g.Where(h => h.FirstReadAt >= startDate).Select(h => h.ComicId).Distinct().Count(),
                        CompletedComics = g.Where(h => h.IsCompleted).Select(h => h.ComicId).Distinct().Count()
                    })
                    .FirstOrDefaultAsync();

                activities.Add(new MonthlyActivityDTO
                {
                    Year = year,
                    Month = month,
                    TotalChaptersRead = monthlyData?.TotalChapters ?? 0,
                    TotalReadingTimeHours = (monthlyData?.TotalReadingTime ?? 0) / 3600,
                    ActiveDays = monthlyData?.ActiveDays ?? 0,
                    NewComicsStarted = monthlyData?.NewComics ?? 0,
                    ComicsCompleted = monthlyData?.CompletedComics ?? 0
                });
            }

            return activities;
        }

        private int CalculateCurrentStreak(List<DateTime> loginDates)
        {
            if (!loginDates.Any()) return 0;

            var today = DateTime.Today;
            var streak = 0;

            for (var date = today; date >= loginDates.First(); date = date.AddDays(-1))
            {
                if (loginDates.Contains(date))
                {
                    streak++;
                }
                else if (date != today) // Allow for today not being included yet
                {
                    break;
                }
            }

            return streak;
        }

        private int CalculateLongestStreak(List<DateTime> loginDates)
        {
            if (!loginDates.Any()) return 0;

            var maxStreak = 1;
            var currentStreak = 1;

            for (int i = 1; i < loginDates.Count; i++)
            {
                if (loginDates[i] == loginDates[i - 1].AddDays(1))
                {
                    currentStreak++;
                    maxStreak = Math.Max(maxStreak, currentStreak);
                }
                else
                {
                    currentStreak = 1;
                }
            }

            return maxStreak;
        }
    }
}
