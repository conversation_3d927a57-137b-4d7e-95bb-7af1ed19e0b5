import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Comic, ComicList, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { Subscription } from 'rxjs';
import { UrlService } from '@services/url.service';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';

@Component({
    selector: 'app-comic-hot',
    templateUrl: './comic-hot.component.html',
    styleUrl: './comic-hot.component.scss',
    standalone: false
})
export class ComicHotComponent extends OptimizedBaseComponent implements OnInit {
  listComics?: Comic[] | null;
  totalpage!: number;
  page = 1;
  subscription!: Subscription;
  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    override cd: ChangeDetectorRef,
    private urlService: UrlService,
    private seoService: SeoService,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
    this.setupSeo();
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.page = Number(params['page']) || 1;
      this.fetchHotComics();
    });
  }
  fetchHotComics() {
    this.listComics = [];
    this.comicService.getHotComics(this.page).subscribe((res: IServiceResponse<ComicList>) => {
      if (res.data) {
        this.listComics = res.data.comics;
        this.totalpage = res.data.totalpage;
        this.cd.detectChanges();
      }
    });
  }

  onChangePage(page: number) {
    this.page = page;
    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  setupSeo() {
    this.seoService.setTitle('Truyện tranh hot tại Mê truyện mới - MeTruyenMoi');
    this.seoService.addTags([
      {
        name: 'description',
        content:
          'Truyện tranh hot - Tất cả truyện đang hót nhất hiện nay đều có thể tìm thể tại Mê truyện mới - MeTruyenMoi',
      },
      {
        property: 'og:title',
        content: 'Truyện tranh hot tại Mê truyện mới - MeTruyenMoi',
      },
      {
        property: 'og:description',
        content:
          'Truyện tranh hot - Tất cả truyện đang hót nhất hiện nay đều có thể tìm thể tại Mê truyện mới - MeTruyenMoi',
      },
    ]);
    this.seoService.updateLink('canonical', `${this.urlService.baseUrl}/truyen-hot`);

  }
}
