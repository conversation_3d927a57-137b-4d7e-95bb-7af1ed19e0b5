using ComicAPI.Enums;
using ComicAPI.Services;
using System.Security.Claims;
using System.Text.Json;

namespace ComicAPI.Middleware
{
    public class ActivityTrackingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ActivityTrackingMiddleware> _logger;

        public ActivityTrackingMiddleware(RequestDelegate next, ILogger<ActivityTrackingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IUserActivityService activityService)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                await _next(context);
            }
            finally
            {
                // Track activity after request completion
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await TrackRequestActivity(context, activityService, startTime);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error tracking activity for request {Path}", context.Request.Path);
                    }
                });
            }
        }

        private async Task TrackRequestActivity(HttpContext context, IUserActivityService activityService, DateTime startTime)
        {
            // Only track for authenticated users
            if (!context.User?.Identity?.IsAuthenticated ?? true)
                return;

            var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out int userId))
                return;

            var path = context.Request.Path.Value?.ToLower() ?? "";
            var method = context.Request.Method;
            var statusCode = context.Response.StatusCode;
            var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;

            // Determine activity type based on request path and method
            var activityType = DetermineActivityType(path, method);
            if (activityType == null)
                return; // Skip tracking for non-relevant endpoints

            // Extract entity information from path
            var (entityType, entityId) = ExtractEntityInfo(path, context.Request.Query);

            // Create metadata
            var metadata = new Dictionary<string, object>
            {
                ["path"] = path,
                ["method"] = method,
                ["statusCode"] = statusCode,
                ["duration"] = duration,
                ["userAgent"] = context.Request.Headers["User-Agent"].ToString(),
                ["ipAddress"] = GetClientIpAddress(context)
            };

            // Add query parameters for relevant endpoints
            if (context.Request.Query.Any())
            {
                var queryParams = context.Request.Query
                    .Where(q => IsRelevantQueryParam(q.Key))
                    .ToDictionary(q => q.Key, q => q.Value.ToString());
                
                if (queryParams.Any())
                    metadata["queryParams"] = queryParams;
            }

            // Track the activity
            activityService.TrackActivityBackground(userId, activityType.Value, entityType, entityId, metadata);

            // Update user last activity
            activityService.UpdateUserLastActivityBackground(userId);

            // Update session activity if session exists
            var sessionId = GetSessionId(context);
            if (!string.IsNullOrEmpty(sessionId))
            {
                var incrementPages = ShouldIncrementPages(path);
                var incrementComics = ShouldIncrementComics(path);
                var incrementChapters = ShouldIncrementChapters(path);

                if (incrementPages || incrementComics || incrementChapters)
                {
                    await activityService.UpdateSessionActivityAsync(sessionId, incrementPages, incrementComics, incrementChapters);
                }
            }
        }

        private UserActivityType? DetermineActivityType(string path, string method)
        {
            // Login/Logout
            if (path.Contains("/auth/login") && method == "POST")
                return UserActivityType.Login;
            if (path.Contains("/auth/logout") && method == "POST")
                return UserActivityType.Logout;

            // Comic related
            if (path.Contains("/comics") && method == "GET")
            {
                if (path.Contains("/search"))
                    return UserActivityType.Search;
                if (path.Contains("/top"))
                    return UserActivityType.ViewTopComics;
                if (path.Contains("/recommend"))
                    return UserActivityType.ViewRecommendations;
                return UserActivityType.ViewComic;
            }

            // Chapter reading
            if (path.Contains("/chapter") && method == "GET")
                return UserActivityType.ReadChapter;
            if (path.Contains("/comic/view_exp") && method == "GET")
                return UserActivityType.ReadChapter;

            // Follow/Unfollow
            if (path.Contains("/follow") && method == "POST")
                return UserActivityType.FollowComic;
            if (path.Contains("/unfollow") && method == "POST")
                return UserActivityType.UnfollowComic;

            // Voting
            if (path.Contains("/vote") && method == "POST")
                return UserActivityType.VoteComic;

            // Comments
            if (path.Contains("/comment") && method == "POST")
                return UserActivityType.AddComment;
            if (path.Contains("/comment") && method == "DELETE")
                return UserActivityType.DeleteComment;

            // Profile
            if (path.Contains("/user/profile") && method == "PUT")
                return UserActivityType.UpdateProfile;
            if (path.Contains("/user/password") && method == "PUT")
                return UserActivityType.ChangePassword;

            // Genres
            if (path.Contains("/genres") && method == "GET")
                return UserActivityType.ViewGenre;

            // Notifications
            if (path.Contains("/notifications") && method == "GET")
                return UserActivityType.ViewNotifications;

            return null; // Don't track this activity
        }

        private (string? entityType, int? entityId) ExtractEntityInfo(string path, IQueryCollection query)
        {
            // Extract comic ID from path or query
            if (path.Contains("/comic/") || query.ContainsKey("comicId"))
            {
                var comicIdStr = ExtractIdFromPath(path, "/comic/") ?? query["comicId"].FirstOrDefault();
                if (int.TryParse(comicIdStr, out int comicId))
                    return ("Comic", comicId);
            }

            // Extract chapter ID from path or query
            if (path.Contains("/chapter/") || query.ContainsKey("chapterId"))
            {
                var chapterIdStr = ExtractIdFromPath(path, "/chapter/") ?? query["chapterId"].FirstOrDefault();
                if (int.TryParse(chapterIdStr, out int chapterId))
                    return ("Chapter", chapterId);
            }

            // Extract genre ID from query
            if (query.ContainsKey("genre") && int.TryParse(query["genre"], out int genreId))
                return ("Genre", genreId);

            return (null, null);
        }

        private string? ExtractIdFromPath(string path, string prefix)
        {
            var index = path.IndexOf(prefix);
            if (index == -1) return null;

            var start = index + prefix.Length;
            var end = path.IndexOf('/', start);
            if (end == -1) end = path.Length;

            var idStr = path.Substring(start, end - start);
            return string.IsNullOrEmpty(idStr) ? null : idStr;
        }

        private bool IsRelevantQueryParam(string key)
        {
            var relevantParams = new[] { "page", "step", "sort", "status", "genre", "q", "comicId", "chapterId" };
            return relevantParams.Contains(key.ToLower());
        }

        private string GetClientIpAddress(HttpContext context)
        {
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress ?? "Unknown";
        }

        private string? GetSessionId(HttpContext context)
        {
            // Try to get session ID from various sources
            var sessionId = context.Request.Headers["X-Session-Id"].FirstOrDefault();
            if (string.IsNullOrEmpty(sessionId))
                sessionId = context.Request.Cookies["SessionId"];
            if (string.IsNullOrEmpty(sessionId))
                sessionId = context.Session.Id;

            return sessionId;
        }

        private bool ShouldIncrementPages(string path)
        {
            // Increment pages for any GET request that's not an API call
            return !path.StartsWith("/api/") && !path.Contains("/static/") && !path.Contains("/assets/");
        }

        private bool ShouldIncrementComics(string path)
        {
            return path.Contains("/comic/") && !path.Contains("/comics");
        }

        private bool ShouldIncrementChapters(string path)
        {
            return path.Contains("/chapter/") || path.Contains("/comic/view_exp");
        }
    }

    // Extension method to register the middleware
    public static class ActivityTrackingMiddlewareExtensions
    {
        public static IApplicationBuilder UseActivityTracking(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ActivityTrackingMiddleware>();
        }
    }
}
