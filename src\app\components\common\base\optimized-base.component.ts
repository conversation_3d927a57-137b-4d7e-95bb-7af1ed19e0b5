import { 
  Component, 
  OnDestroy, 
  ChangeDetectorRef, 
  Inject, 
  PLATFORM_ID,
  ChangeDetectionStrategy 
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Subscription, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

/**
 * Base component with performance optimizations
 * - Automatic subscription management
 * - OnPush change detection
 * - Memory leak prevention
 * - Platform-aware operations
 */
@Component({
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export abstract class OptimizedBaseComponent implements OnDestroy {
  protected destroy$ = new Subject<void>();
  protected subscriptions = new Set<Subscription>();
  protected isBrowser: boolean;
  protected isServer: boolean;

  constructor(
    protected cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected platformId: object,
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.isServer = !this.isBrowser;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    // Clean up subscriptions
    this.subscriptions.forEach(sub => {
      if (!sub.closed) {
        sub.unsubscribe();
      }
    });
    this.subscriptions.clear();
  }

  /**
   * Add subscription with automatic cleanup
   */
  protected addSubscription(subscription: Subscription): void {
    this.subscriptions.add(subscription);
  }

  /**
   * Safe change detection trigger
   */
  protected safeDetectChanges(): void {
      this.cd.detectChanges();
  }

  /**
   * Mark for check with safety
   */
  protected safeMarkForCheck(): void {
      this.cd.markForCheck();
  }

  /**
   * Browser-only operations
   */
  protected runInBrowser(callback: () => void): void {
    if (this.isBrowser) {
      callback();
    }
  }
  protected runInServer(callback: () => void): void {
    if (this.isServer) {
      callback();
    }
  }

  /**
   * Debounced function execution
   */
  protected debounce(func: Function, delay: number): Function {
    let timeoutId: any;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * Throttled function execution
   */
  protected throttle(func: Function, delay: number): Function {
    let lastCall = 0;
    return (...args: any[]) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }

  /**
   * TrackBy function for ngFor optimization
   */
  protected trackById = (index: number, item: any): any => {
    return item?.id ?? index;
  };

  /**
   * TrackBy function for ngFor with index
   */
  protected trackByIndex = (index: number): number => index;

  /**
   * Safe async operation with takeUntil
   */
  protected takeUntilDestroy() {
    return takeUntil(this.destroy$);
  }



  /**
   * Request animation frame wrapper
   */
  protected requestAnimationFrame(callback: () => void): void {
    if (this.isBrowser && 'requestAnimationFrame' in window) {
      requestAnimationFrame(callback);
    } else {
      setTimeout(callback, 16); // Fallback for SSR
    }
  }


  /**
   * Resize Observer wrapper
   */
  protected createResizeObserver(
    callback: ResizeObserverCallback
  ): ResizeObserver | null {
    if (this.isBrowser && 'ResizeObserver' in window) {
      return new ResizeObserver(callback);
    }
    return null;
  }
}
