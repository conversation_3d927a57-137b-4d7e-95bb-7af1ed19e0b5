// Comic Content Container
.comic-content {
    @apply h-full w-full flex flex-col relative border-spacing-3 overflow-hidden text-secondary-100 dark:text-white;
}

// Background Elements
.comic-bg {
    @apply absolute inset-0 h-full border-spacing-2 z-[1] blur-sm;
}

.comic-bg-image {
    @apply w-full h-[200%] bg-cover bg-black bg-no-repeat bg-top -translate-y-[20%];
}

.comic-bg-overlay {
    @apply absolute inset-0 bg-black opacity-30;
}

.comic-bg-loading {
    @apply w-full h-full bg-neutral-400 object-cover object-center;
}

// Breadcrumb
.breadcrumb {
    @apply md:container mx-auto z-10 mt-3 mb-5 text-neutral-100 hover:text-white;
}

// Comic Detail Section
.comic-detail {
    @apply lg:flex xl:flex rounded-t-3xl z-[2] mx-auto md:container md:w-3/4 w-full lg:w-full mt-8 lg:mt-5 backdrop-blur-sm bg-gradient-to-t from-white to-white/80 dark:from-dark-bg dark:to-dark-bg/50;
}

// Image Section
.comic-image-section {
    @apply lg:ml-10;
}

.comic-image-container {
    @apply flex justify-center relative h-[280px] lg:h-auto;
}

.comic-bg-img {
    @apply w-[200px] h-[300px] flex shadow-md rounded absolute lg:relative bottom-8 translate-y-4 lg:translate-y-0 lg:bottom-5 border-neutral-200 border-2 overflow-hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        transform: skewX(-25deg);
        height: 100%;
        background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
        animation: none;
    }

    &:hover::before {
        animation: slideOnHover 0.5s ease-in-out forwards;
    }

    @keyframes slideOnHover {
        0% {
            left: -100%;
        }

        100% {
            left: 100%;
        }
    }
}

.comic-cover-image {
    @apply w-[196px] h-[300px] object-cover;
}

// Follow Button Section
.follow-button-container {
    @apply mb-3 flex justify-center;
}

.btn-follow {
    @apply py-1 px-4 gap-1 bg-sky-600 text-white rounded hover:bg-sky-500 flex items-center transition-colors duration-150;

    &:focus {
        @apply ring-2 ring-sky-300 ring-opacity-50;
    }
}

.keywords {
    @apply mt-2 inline-block space-x-1 gap-2 items-center px-2;

    .keyword {
        @apply px-2 py-0.5 text-sm h-fit bg-neutral-100 dark:bg-neutral-700 rounded text-neutral-700 dark:text-neutral-300 cursor-pointer;
    }
}

.btn-unfollow {
    @apply py-1 px-4 border-red-500 border text-red-500 rounded hover:opacity-75 flex space-x-1 gap-1 items-center transition-all duration-150;

    &:focus {
        @apply ring-2 ring-red-300 ring-opacity-50;
    }
}

// Modern Comic Info Section - Comic Website Design
.comic-detail-info {
    @apply flex flex-col justify-center items-center lg:items-start mx-4 my-3 mb-5 gap-0.5;
}

.comic-detail-title {
    @apply font-bold uppercase text-xl lg:text-2xl text-center lg:text-left;
    @apply text-gray-700 dark:text-white;
    @apply text-pretty leading-tight;
}

.comic-update {
    @apply dark:text-neutral-200 text-sm mt-1;
    @apply inline-block;
}

.comic-other-title {
    @apply text-neutral-600 dark:text-neutral-200;
    @apply capitalize text-sm lg:text-base;
    @apply line-clamp-2 lg:line-clamp-1;
    @apply font-medium;
}

.comic-author {
    @apply font-light dark:text-neutral-200 text-sm;
}

// Modern Star Rating - Comic Website Design
.star-rating-container {
    @apply flex items-center col-span-3;
}

.star-rating-item {
    @apply relative cursor-pointer;
    @apply mr-1;
    @apply transform transition-all duration-200;
    @apply hover:scale-125 active:scale-110;
    @apply focus:outline-none focus:ring-2 focus:ring-amber-300/50;
    @apply rounded-full;
}

.star-icon {
    @apply fill-current transition-all duration-200;
    @apply w-4 h-4;
    @apply drop-shadow-sm;

    &.star-empty {
        @apply text-neutral-300 dark:text-neutral-600;
        @apply hover:text-amber-300 dark:hover:text-amber-400;
    }

    &.star-filled {
        @apply text-amber-400;
        @apply hover:text-amber-500;
        @apply filter drop-shadow-md;
    }
}

.star-fill-overlay {
    @apply absolute top-0 left-0 h-full overflow-hidden;
}

// Modern Comic Info List - Comic Website Design
.comic-info-list {
    @apply space-y-2 text-sm mt-1;
}

.list-main-info {
    @apply flex flex-wrap justify-center lg:justify-start gap-3 sm:gap-4 text-sm;
}

.info-item {
    @apply flex flex-col items-center lg:items-start;
}

.info-label {
    @apply sm:text-sm dark:text-neutral-200;
}

.info-label-text {
    @apply text-xs sm:text-sm text-gray-800 dark:text-neutral-200;
}

.info-value {
    @apply flex space-x-1 items-center text-gray-800 dark:text-neutral-200;
}

.info-icon {
    @apply size-4 sm:size-5;
}

.info-number {
    @apply font-semibold;
}

// Status Indicators
.status-container {
    @apply flex space-x-2 items-center font-semibold;
}

.status-indicator {
    @apply relative flex h-2 w-2 justify-center items-center;
}

.status-ping {
    @apply animate-ping absolute inline-flex h-full w-full rounded-full opacity-65;

    &.status-ping-completed {
        @apply bg-lime-500;
    }
}

.status-dot {
    @apply relative inline-flex rounded-full h-2 w-2;

    &.status-dot-ongoing {
        @apply bg-sky-400;
    }
}

.status-check-icon {
    @apply h-3 w-3;
}

.status-text {
    @apply text-sm;
}

.status-ongoing {
    .status-ping {
        @apply bg-sky-400;
    }
}

.status-completed {
    .status-ping {
        @apply bg-lime-500;
    }
}

// Modern Genre List - Comic Website Design
.list-genre {
    @apply text-sm sm:text-base flex justify-center lg:justify-normal flex-wrap space-x-2;
}

.genre-item {
    @apply uppercase font-semibold hover:text-white mt-1 px-2 text-neutral-600 dark:text-neutral-100 dark:border-neutral-500 hover:bg-primary-100 rounded border-neutral-300 border border-dashed;

    &:focus {
        @apply ring-2 ring-primary-100 ring-opacity-50;
    }
}

// Description Section
.comic-description-section {
    @apply lg:text-left lg:w-full mx-3 lg:m-0;
}

.comic-description {
    @apply mt-2 text-sm lg:mr-4;

    &.description-collapsed {
        @apply line-clamp-3;
    }

    &.description-expanded {
        @apply line-clamp-none;
    }
}

.btn-viewmore {
    @apply underline text-sm font-semibold dark:text-neutral-500 hover:text-neutral-700 transition-colors duration-150;

    &:focus {
        @apply ring-2 ring-neutral-300 ring-opacity-50;
    }
}

// Modern Read Action Section - Comic Website Design
.read-action-container {
    @apply flex space-x-2 mt-3;
}

.btn-recent {
    @apply bg-primary-100 rounded p-2 px-4 hover:opacity-90 text-white flex items-center space-x-2 transition-all duration-150;

    &:focus {
        @apply ring-2 ring-primary-200 ring-opacity-50;
    }
}

.btn-new {
    @apply px-4 p-2 border dark:text-white dark:bg-neutral-800 border-neutral-600 cursor-pointer rounded text-black font-semibold text-sm hover:text-white hover:bg-secondary-100 transition-all duration-150;

    &:focus {
        @apply ring-2 ring-opacity-50;
    }
}

.read-action-icon {
    @apply w-4 h-4;
}

.read-action-text {
    @apply font-semibold text-sm;
}


.star-rating-item {
    transition: transform 0.2s ease-out;

    &:hover {
        transform: scale(1.2);
    }
}