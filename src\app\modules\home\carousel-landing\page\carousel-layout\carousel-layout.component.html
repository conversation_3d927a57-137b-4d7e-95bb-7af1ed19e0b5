<div class="block-item">
  <a
    *ngFor="let comic of comics; let i = index; trackBy: trackByComicId"
    [routerLink]="getComicUrl(comic)"
    [class]="classes[i]"
  >
    <img
      (mouseenter)="OnComicHover(comic)"
      (mouseleave)="OnComicLeave()"
      class="item-image"
      src="{{ comic.coverImage }}"
      alt="{{ comic.title }}"
      onerror="this.src='/option2.png'"
    />
  </a>
</div>
