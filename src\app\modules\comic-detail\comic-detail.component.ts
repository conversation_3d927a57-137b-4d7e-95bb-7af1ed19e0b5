// Remove unused import
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, Optional, PLATFORM_ID, RESPONSE_INIT } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';

import { Chapter, Comic, IServiceResponse } from '@schema';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import { UrlService } from '@services/url.service';
import { ServiceUtility } from '@services/utils.service';

@Component({
  selector: '[app-comic-detail]',
  templateUrl: './comic-detail.component.html',
  styleUrl: './comic-detail.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComicDetailComponent extends OptimizedBaseComponent implements OnInit {
  getChapterUrl(comic: Comic, chapter: Chapter): any {
    return this.urlService.getChapterDetailUrl(comic, chapter);
  }
  getComicUrl(comic: Comic): any {
    return this.urlService.getComicDetailUrl(comic);
  }
  // Component state
  comic!: Comic;
  isFollowed = false;
  allchapters: Chapter[] = [];
  lastChapter?: Chapter;
  similarComics: Comic[] = [];
  isOpen = false;
  comicHistory?: Comic;
  latestHistoryChapter?: Chapter;
  isChapterLoading = false;
  // Constants
  private readonly FOLLOW_COOLDOWN = 3000;

  // Performance optimizations
  private debouncedToggleDescription!: Function;
  private followRequestTime = 0;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private accountService: AccountService,
    private toastService: ToastService,
    private seoService: SeoService,
    private historyService: HistoryService,
    private popupService: PopupService,
    private urlService: UrlService,
    @Optional() @Inject(RESPONSE_INIT) private serverResponse: ResponseInit,

    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
  ) {
    super(cdr, platformId);
    this.setupDebouncedMethods();
  }

  // Computed properties for better performance
  stars: number[] = [100, 100, 100, 100, 100];


  get hasChapters(): boolean {
    return this.allchapters.length > 0;
  }

  get hasSimilarComics(): boolean {
    return this.similarComics.length > 0;
  }

  get isComicFollowed(): boolean {
    return this.comic?.isFollow || false;
  }

  get hasComicHistory(): boolean {
    return !!this.comicHistory;
  }

  get canShowReadFromBeginning(): boolean {
    return !this.comicHistory && this.hasChapters;
  }

  get canShowContinueReading(): boolean {
    return this.hasComicHistory && !!this.latestHistoryChapter;
  }

  get followButtonClass(): string {
    return this.isComicFollowed ? 'btn-unfollow' : 'btn-follow';
  }

  get statusIndicatorClass(): string {
    return this.comic?.status === 0 ? 'status-ongoing' : 'status-completed';
  }

  get descriptionClass(): string {
    return this.isOpen ? 'description-expanded' : 'description-collapsed';
  }

  // TrackBy functions for ngFor optimization
  trackByStarIndex = (index: number): number => index;

  trackByGenreId = (_index: number, genre: any): number => genre.id;

  private setupDebouncedMethods(): void {
    this.debouncedToggleDescription = this.debounce(() => {
      this.performToggleDescription();
    }, 150);
  }
  private performToggleDescription(): void {
    this.isOpen = !this.isOpen;
    this.safeMarkForCheck();
  }

  ngOnInit(): void {
    this.addSubscription(
      this.route.data.pipe(this.takeUntilDestroy()).subscribe(({ comicRes } : any) => {
        comicRes = comicRes as IServiceResponse<Comic>;
        const { response, redirect } = comicRes;

        if (redirect) {
          this.router.navigate(redirect);
          return;
        }
        if (response === null || response.data === null) {
          this.router.navigate(['/']);
          return;
        }
        this.comic = response.data;
        this.isFollowed = this.comic?.isFollow || false;
        this.SetUpSEO(this.comic!);
        const chapters = this.comic?.chapters ?? [];
        this.lastChapter = chapters[chapters.length - 1];
        this.allchapters = this.comic?.chapters ?? [];
        this.isChapterLoading = true;

        this.stars = this.stars.map((star, index) => {
          return this.getStarWidth(index + 1);
        });

        this.runInBrowser(() => {
          this.getChapters();
          this.getSimilarComics();
          this.setupComicHistory();
        });

        this.safeMarkForCheck();
      })
    );
  }

  private setupComicHistory(): void {
    this.comicHistory = this.historyService.GetHistory(this.comic!.id);
    if (this.comicHistory?.chapters?.length) {
      this.latestHistoryChapter = this.comicHistory.chapters.reduce(
        (maxSlugChapter, currentChapter) => {
          return Number(currentChapter.slug) > Number(maxSlugChapter.slug)
            ? currentChapter
            : maxSlugChapter;
        },
        this.comicHistory.chapters[0],
      );
    }
  }

  getChapters(): void {
    this.isChapterLoading = true;
    this.addSubscription(
      this.comicService.getChapters(this.comic!.id)
        .pipe(this.takeUntilDestroy())
        .subscribe((res: any) => {
          this.allchapters = res.data;
          this.isChapterLoading = false;
          this.safeMarkForCheck();
        })
    );
  }

  getSimilarComics(): void {
    this.addSubscription(
      this.comicService.getSimilarComic(this.comic!.id)
        .pipe(this.takeUntilDestroy())
        .subscribe((res: any) => {
          this.similarComics = res.data;
          this.safeMarkForCheck();
        })
    );
  }

  Follow(isFollow: boolean): void {
    const now = Date.now();
    if (!this.accountService.isAuthenticated()) {
      this.router.navigate(['/auth/login']);
      return;
    }
    if (this.followRequestTime + this.FOLLOW_COOLDOWN > now) {
      this.toastService.show(ToastType.Info, `Thao tác quá nhanh!`);
      return;
    }
    this.followRequestTime = now;
    this.accountService
      .Follow(this.comic!.id, isFollow)
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        if (res.status === 1) {
          this.comic!.isFollow = !this.comic!.isFollow;
          this.toastService.show(
            ToastType.Success,
            this.comic!.isFollow ? 'Đã theo dõi' : 'Đã hủy theo dõi',
          );
        } else {
          this.toastService.show(ToastType.Error, res.message!);
        }
        this.cd.markForCheck();

      });
  }

  toggleDescription(): void {
    this.debouncedToggleDescription();
  }

  getHistoryChapter() {
    if (!this.comicHistory || !this.latestHistoryChapter) {
      return;
    }
    return this.latestHistoryChapter?.title?.match(/[\.\d]+/iu);
  }

  SetUpSEO(comic: Comic) {
    const latestChapter = comic.chapters?.[0]?.slug || 'Mới nhất';
    const title = `${comic.title} [Tới Chương ${latestChapter}] - Đọc Truyện Tranh Online`;
    const description = this.generateSEODescription(comic);
    const url = this.urlService.getFullComicUrl(comic);

    const seoData = {
      title,
      description,
      type: 'book' as const,
      url,
      image: comic.coverImage || `${this.urlService.baseUrl}/logo.png`,
      author: comic.author,
      publishedTime: comic.updateAt,
      modifiedTime: comic.updateAt,
      section: 'Truyện Tranh',
      tags: comic.genres?.map(g => g.title) || [],
      siteName: 'MeTruyenMoi',
      canonical: url,
      twitterCard: 'summary_large_image' as const
    };

    this.seoService.setSEOData(seoData);

    // Add comic structured data
    const comicSchema = this.seoService.generateComicSchema(comic);

    // Add breadcrumb structured data
    const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: this.urlService.baseUrl },
      { name: comic.title, url: this.urlService.getFullComicUrl(comic) },
    ]);

    // Add FAQ structured data for comic using SEO service
    const faqSchema = this.seoService.generateComicFAQSchema(comic);

    // Add organization schema
    const organizationSchema = this.seoService.generateOrganizationSchema();

    // Combine all schemas
    const combinedSchema = [comicSchema, breadcrumbSchema, faqSchema, organizationSchema];
    this.seoService.addStructuredData(combinedSchema);
  }

  private generateSEODescription(comic: Comic): string {
    const baseDescription = comic.description
      ? ServiceUtility.fillSeoDescription(comic.description, { title: comic.title })
      : `Đọc truyện tranh ${comic.title} online miễn phí tại MeTruyenMoi`;
    return `${baseDescription}. Cập nhật nhanh, chất lượng cao.`;
  }

  rateStar(starIndex: number) {
    this.popupService.showRateComic({
      comicID: this.comic!.id,
      initialRating: starIndex,
    });
  }
  getStarWidth(index: number): number {
    if (index <= this.comic!.rating) {
      return 100;
    }
    if (index - this.comic!.rating < 1) {
      return (1 - (index - this.comic!.rating)) * 100;
    }
    return 0;
  }

  isAgeLimit(): boolean {
    return false;
    console.log(this.comic?.genres);

    return this.comic?.genres?.some(g => g.ageLimit) || false;
  }
}
