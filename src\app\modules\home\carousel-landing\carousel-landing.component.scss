
// Optimized Carousel Component - Tailwind @apply with responsive utilities

// Desktop Carousel
.desktop-carousel {
  @apply w-full h-full absolute overflow-hidden;
  @apply hidden lg:flex;
}

// Navigation Controls
.nav-left {
  @apply absolute z-10 h-full w-1/4 cursor-pointer;
  @apply bg-black/40 hover:bg-black/60;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .nav-icon {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
    @apply w-20 h-20 text-white opacity-10;
    @apply transition-opacity duration-200;
  }

  &:hover .nav-icon {
    @apply opacity-80;
  }
}

.nav-right {
  @apply absolute z-10 h-full w-1/4 cursor-pointer right-0;
  @apply bg-black/40 hover:bg-black/60;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .nav-icon {
    @apply absolute top-1/2 right-1/2 translate-x-1/2 -translate-y-1/2;
    @apply w-20 h-20 text-white opacity-10;
    @apply transition-opacity duration-200;
  }

  &:hover .nav-icon {
    @apply opacity-80;
  }
}

// Comic Details Overlay
.details-overlay {
  @apply absolute left-1/4 w-2/4 h-full z-10;
  @apply bg-black/80 text-white overflow-hidden pointer-events-none;
  @apply transition-all duration-300;
}

.details-content {
  @apply flex flex-col p-3 gap-2;
}

.comic-header {
  @apply flex justify-between items-start gap-2;
}

.comic-title {
  @apply font-bold uppercase text-base leading-tight;
}

.status-container {
  @apply flex items-center gap-2 text-sm;
}

.status-ongoing {
  @apply w-1 h-1 rounded-full bg-sky-400 animate-ping opacity-75;
}

.status-completed {
  @apply w-1.5 h-1.5 animate-ping opacity-75;
}

// Genre Tags
.genre-tags {
  @apply flex flex-wrap gap-1;
}

.tag-primary {
  @apply bg-primary-100 text-white text-xs font-bold rounded px-2 py-0.5 uppercase cursor-pointer;
}

.tag-secondary {
  @apply bg-neutral-600 text-white text-xs font-semibold rounded px-2 py-0.5 uppercase cursor-pointer;
}

// Comic Stats
.comic-stats {
  @apply flex gap-3;
}

.stat-item {
  @apply flex items-center gap-1 text-sm;

  &:first-child {
    @apply text-yellow-500;
  }
}

.stat-icon {
  @apply w-4 h-4;
}

.comic-description {
  @apply mt-2;
}

.description-text {
  @apply text-sm leading-relaxed;
}

// Carousel Items
.carousel-item {
  @apply absolute top-0 h-full w-1/3 flex;
  @apply transition-[left] duration-700 ease-out;
}

.loading-container {
  @apply w-full h-full flex items-center justify-center;
}

// Mobile Carousel
.mobile-carousel {
  @apply absolute w-full h-full flex overflow-hidden bg-black;
  @apply lg:hidden;
}

.mobile-nav-left {
  @apply absolute top-1/2 left-2 z-10 w-12 h-12 cursor-pointer;
  @apply -translate-y-1/2 rounded-xl overflow-hidden;
  @apply bg-black/40 hover:bg-black/60 hover:scale-110 active:scale-95;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .mobile-nav-icon {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
    @apply w-10 h-10 text-white opacity-40;
    @apply transition-opacity duration-200;
  }

  &:hover .mobile-nav-icon {
    @apply opacity-80;
  }
}

.mobile-nav-right {
  @apply absolute top-1/2 right-2 z-10 w-12 h-12 cursor-pointer;
  @apply -translate-y-1/2 rounded-xl overflow-hidden;
  @apply bg-black/40 hover:bg-black/60 hover:scale-110 active:scale-95;
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50;

  .mobile-nav-icon {
    @apply absolute top-1/2 right-1/2 translate-x-1/2 -translate-y-1/2;
    @apply w-10 h-10 text-white opacity-40;
    @apply transition-opacity duration-200;
  }

  &:hover .mobile-nav-icon {
    @apply opacity-80;
  }
}

.mobile-item {
  @apply absolute top-0 h-full block py-1 px-0.5;
  @apply transition-[left] duration-700 ease-out;
}

.mobile-image {
  @apply w-full h-full object-cover;
}

// Swiper Component
.swiper-container {
  @apply absolute z-10 right-1/2 translate-x-1/2;
  @apply -bottom-4 md:-bottom-5;
}

.block-item {
  @apply relative overflow-hidden bg-dark-bg shadow-md border-x-2 border-y-4 border-dark-bg grid grid-cols-4 grid-rows-4 gap-1 w-full h-full;
}