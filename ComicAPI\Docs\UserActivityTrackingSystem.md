# User Activity Tracking System

## Tổng quan

Hệ thống truy vết hành vi người dùng được thiết kế để theo dõi và phân tích hoạt động của người dùng trên nền tảng Comic API một cách tối ưu và không ảnh hưởng đến hiệu suất chính của hệ thống.

## Kiến trúc hệ thống

### 1. Models và Database Schema

#### UserActivity
- Lưu trữ các hoạt động tổng quát của người dùng
- <PERSON><PERSON> gồm: login, logout, view comic, read chapter, follow, vote, comment, etc.
- Hỗ trợ metadata dạng JSON để lưu thông tin bổ sung

#### UserReadingHistory
- <PERSON> dõi chi tiết lịch sử đọc truyện
- Lưu trữ: ti<PERSON><PERSON> đ<PERSON> đ<PERSON>, thờ<PERSON> gian đ<PERSON>, s<PERSON> lầ<PERSON> đọ<PERSON>, trang đã đọc
- Tự động cập nhật khi người dùng đọc lại

#### UserLoginHistory
- L<PERSON><PERSON> sử đăng nhập/đăng xuất
- Thông tin: IP, User Agent, thiết bị, vị trí, thời gian session

#### UserSessionActivity
- Theo dõi session của người dùng
- Thống kê: số trang xem, số truyện xem, số chương đọc trong session

### 2. Services Layer

#### UserActivityService
- Service chính để tracking activities
- Hỗ trợ batch processing và background queue
- Tối ưu performance với caching

#### ActivityAnalyticsService
- Phân tích và tổng hợp dữ liệu
- Tạo báo cáo thống kê
- Hỗ trợ predictive analytics

#### ActivityBackgroundService
- Xử lý background tasks
- Cleanup dữ liệu cũ
- Refresh analytics cache

### 3. Repository Pattern
- UserActivityRepository: Tối ưu truy vấn database
- Hỗ trợ bulk operations
- Cache management

## Tính năng chính

### 1. Activity Tracking
```csharp
// Track activity đồng bộ (high priority)
await activityService.TrackActivityAsync(userId, UserActivityType.ReadChapter, "Chapter", chapterId);

// Track activity bất đồng bộ (normal priority)
activityService.TrackActivityBackground(userId, UserActivityType.ViewComic, "Comic", comicId);
```

### 2. Reading Progress Tracking
```csharp
// Track tiến độ đọc
await activityService.TrackReadingProgressAsync(userId, comicId, chapterId, 
    pageNumber: 15, readingProgress: 75.0f, readingTimeSeconds: 300, isCompleted: false);
```

### 3. Session Management
```csharp
// Bắt đầu session
var sessionId = await activityService.StartSessionAsync(userId, ipAddress, userAgent);

// Cập nhật session activity
await activityService.UpdateSessionActivityAsync(sessionId, 
    incrementPages: true, incrementComics: true, incrementChapters: false);

// Kết thúc session
await activityService.EndSessionAsync(sessionId);
```

### 4. Analytics và Reporting
```csharp
// Lấy thống kê người dùng
var stats = await analyticsService.GetUserAnalyticsAsync(userId);

// Phân tích hành vi
var patterns = await analyticsService.GetUserBehaviorPatternsAsync(userId);

// Thống kê hệ thống
var systemStats = await analyticsService.GetSystemAnalyticsAsync();
```

## API Endpoints

### Activity Endpoints
- `GET /api/activity/activities` - Lấy danh sách activities
- `GET /api/activity/reading-history` - Lịch sử đọc truyện
- `GET /api/activity/login-history` - Lịch sử đăng nhập
- `GET /api/activity/stats` - Thống kê tổng quan
- `POST /api/activity/track-reading` - Track tiến độ đọc
- `POST /api/activity/track-activity` - Track activity thủ công

### Analytics Endpoints
- `GET /api/analytics/user/overview` - Tổng quan analytics người dùng
- `GET /api/analytics/user/daily-trends` - xu hướng hoạt động hàng ngày
- `GET /api/analytics/user/behavior-patterns` - Phân tích hành vi
- `GET /api/analytics/user/genre-preferences` - Sở thích thể loại
- `GET /api/analytics/user/recommendations` - Gợi ý truyện

### Admin Analytics Endpoints
- `GET /api/analytics/system/overview` - Tổng quan hệ thống
- `GET /api/analytics/system/most-active-users` - Người dùng hoạt động nhất
- `GET /api/analytics/system/most-read-comics` - Truyện được đọc nhiều nhất
- `GET /api/analytics/system/real-time` - Thống kê real-time

## Tối ưu Performance

### 1. Batch Processing
- Activities được queue và xử lý theo batch mỗi 30 giây
- Giảm tải database và tăng throughput

### 2. Background Processing
- Sử dụng IBackgroundTaskQueue cho các tác vụ không cần real-time
- Không block main thread

### 3. Caching Strategy
- Memory cache cho dữ liệu thường xuyên truy cập
- Cache invalidation thông minh
- Distributed cache support (Redis)

### 4. Database Optimization
- Indexes được tối ưu cho các query thường dùng
- Partitioning cho bảng lớn (optional)
- Cleanup tự động dữ liệu cũ

### 5. Middleware Integration
- ActivityTrackingMiddleware tự động track activities
- Minimal overhead cho requests
- Smart activity detection

## Cấu hình

### Database
```sql
-- Chạy script tạo bảng
\i ComicAPI/SqlDB/ActivityTables.sql
```

### Services Registration
```csharp
// Trong Program.cs
builder.Services.AddScoped<IUserActivityRepository, UserActivityRepository>();
builder.Services.AddScoped<IUserActivityService, UserActivityService>();
builder.Services.AddScoped<IActivityAnalyticsService, ActivityAnalyticsService>();
builder.Services.AddActivityBackgroundService();
```

### Middleware
```csharp
// Trong Program.cs
app.UseActivityTracking(); // Thêm sau UseAuthentication và UseAuthorization
```

## Monitoring và Maintenance

### 1. Health Checks
- Background service tự động kiểm tra health
- Log warnings khi có vấn đề

### 2. Cleanup
- Tự động cleanup dữ liệu cũ (mặc định 365 ngày)
- Cleanup inactive sessions (7 ngày)
- Database optimization

### 3. Analytics Refresh
- Cache analytics được refresh định kỳ
- Real-time stats cập nhật mỗi phút
- System analytics cập nhật mỗi giờ

## Security và Privacy

### 1. Data Protection
- IP addresses được hash (optional)
- User agents được sanitize
- Sensitive data không được log

### 2. Access Control
- User chỉ xem được data của mình
- Admin endpoints yêu cầu role Admin
- Rate limiting cho analytics endpoints

### 3. Data Retention
- Configurable retention period
- GDPR compliance support
- Data export functionality

## Testing

### Unit Tests
```bash
# Chạy tests
dotnet test ComicAPI.Tests/UserActivityServiceTests.cs
```

### Performance Tests
- Load testing với 1000+ concurrent activities
- Memory usage monitoring
- Database performance profiling

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - Kiểm tra cache size limits
   - Adjust batch processing intervals
   - Monitor queue sizes

2. **Slow Analytics Queries**
   - Check database indexes
   - Optimize query patterns
   - Consider data archiving

3. **Missing Activities**
   - Check background service status
   - Verify middleware registration
   - Monitor error logs

### Monitoring Queries
```sql
-- Check activity volume
SELECT DATE(created_at), COUNT(*) 
FROM user_activities 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at);

-- Check active sessions
SELECT COUNT(*) FROM user_session_activities WHERE is_active = true;

-- Check queue health
SELECT COUNT(*) FROM user_activities WHERE created_at >= NOW() - INTERVAL '1 minute';
```

## Future Enhancements

1. **Machine Learning Integration**
   - User behavior prediction
   - Personalized recommendations
   - Anomaly detection

2. **Real-time Dashboards**
   - WebSocket integration
   - Live activity feeds
   - Real-time notifications

3. **Advanced Analytics**
   - Cohort analysis
   - Funnel analysis
   - A/B testing support

4. **Data Warehouse Integration**
   - ETL pipelines
   - Historical data analysis
   - Business intelligence tools
