
using System.Net;
using ComicAPI.Classes;
using Microsoft.Extensions.Options;
namespace ComicAPI.Services
{
    public class UrlService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AppSetting _config;
        public string ImgHost => _imgHost;
        private readonly string _imgHost = "https://cdn.anhtruyen.com";
        private readonly string _staticHost = "https://cdn1.anhtruyen.com";
        private readonly string _baseUrl = "https://metruyenmoi.com";
        private readonly string _chatbotUrl = "http://***************:8000/v1/chatbot";
        public string ChatbotUrl => _chatbotUrl;
        public string Host => GetHost();


        public UrlService(IWebHostEnvironment environment, IOptions<AppSetting> options, IHttpContextAccessor httpContextAccessor)
        {
            _environment = environment;
            _config = options.Value;
            // _host = _config.Host ?? _host;
            _imgHost = _config.ImgHost ?? _imgHost;
            _httpContextAccessor = httpContextAccessor;

            // httpContext.HttpContext.Request.Headers.TryGetValue("X-Real-Host", out var host);
        }

        public static bool IsIPAddress(string host)
        {
            return IPAddress.TryParse(host, out _);
        }
        public static bool IsMetruyenMoiDomain(string host)
        {
            return host.Contains("metruyenmoi");
        }

        private string GetHost()
        {
            var context = _httpContextAccessor.HttpContext;

            if (context == null) return _baseUrl;

            if (context.Request.Headers.TryGetValue("X-Real-Host", out var realHost))
            {
                if (IsMetruyenMoiDomain(realHost.ToString()))
                {
                    return $"https://{realHost.ToString()}";
                }
            }
            if (context.Request.Host.HasValue)
            {
                if (IsMetruyenMoiDomain(context.Request.Host.Value))
                {
                    return $"https://{context.Request.Host.Value}";
                }
            }
            return _baseUrl;
        }
        public string GetComicCoverImagePath(string? Image)
        {
            return $"{_staticHost}/coverimg/{Image}";
        }
        public string GetUserImagePath(string? Image)
        {
            return ServiceUtilily.AddTimestampToUrl($"{Host}/AvatarImg/{Image}");
        }
        public string GetPathSaveUserImage()
        {
            return Path.Combine(_environment.ContentRootPath, "wwwroot/AvatarImg"); ;
        }
        public string GetConfirmEmailPath(int UserId, string Code)
        {
            return $"{Host}/auth/confirm-email?UserId={UserId}&Code={Code}";
        }
    }
}