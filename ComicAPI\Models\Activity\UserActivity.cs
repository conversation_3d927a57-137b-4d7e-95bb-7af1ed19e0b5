using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ComicAPI.Enums;

namespace ComicAPI.Models.Activity
{
    public class UserActivity
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        [Required]
        [Column("activity_type")]
        public UserActivityType ActivityType { get; set; }

        [Column("entity_type")]
        public string? EntityType { get; set; } // "Comic", "Chapter", "Comment", etc.

        [Column("entity_id")]
        public int? EntityId { get; set; }

        [Column("metadata")]
        public string? Metadata { get; set; } // JSON data for additional info

        [Column("ip_address")]
        public string? IpAddress { get; set; }

        [Column("user_agent")]
        public string? UserAgent { get; set; }

        [Required]
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("session_id")]
        public string? SessionId { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }

    public class UserReadingHistory
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        [Required]
        [Column("comic_id")]
        public int ComicId { get; set; }

        [Required]
        [Column("chapter_id")]
        public int ChapterId { get; set; }

        [Column("page_number")]
        public int? PageNumber { get; set; }

        [Column("reading_progress")]
        public float ReadingProgress { get; set; } = 0; // 0-100%

        [Column("reading_time_seconds")]
        public int ReadingTimeSeconds { get; set; } = 0;

        [Required]
        [Column("first_read_at")]
        public DateTime FirstReadAt { get; set; } = DateTime.UtcNow;

        [Required]
        [Column("last_read_at")]
        public DateTime LastReadAt { get; set; } = DateTime.UtcNow;

        [Column("read_count")]
        public int ReadCount { get; set; } = 1;

        [Column("is_completed")]
        public bool IsCompleted { get; set; } = false;

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Comic? Comic { get; set; }
        public virtual Chapter? Chapter { get; set; }
    }

    public class UserLoginHistory
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        [Required]
        [Column("login_at")]
        public DateTime LoginAt { get; set; } = DateTime.UtcNow;

        [Column("logout_at")]
        public DateTime? LogoutAt { get; set; }

        [Column("ip_address")]
        public string? IpAddress { get; set; }

        [Column("user_agent")]
        public string? UserAgent { get; set; }

        [Column("device_info")]
        public string? DeviceInfo { get; set; } // JSON data

        [Column("location")]
        public string? Location { get; set; } // City, Country

        [Column("session_duration_seconds")]
        public int? SessionDurationSeconds { get; set; }

        [Column("is_successful")]
        public bool IsSuccessful { get; set; } = true;

        [Column("failure_reason")]
        public string? FailureReason { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }

    public class UserSessionActivity
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        [Required]
        [Column("session_id")]
        public string SessionId { get; set; } = string.Empty;

        [Required]
        [Column("started_at")]
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;

        [Column("last_activity_at")]
        public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;

        [Column("ended_at")]
        public DateTime? EndedAt { get; set; }

        [Column("total_duration_seconds")]
        public int TotalDurationSeconds { get; set; } = 0;

        [Column("pages_visited")]
        public int PagesVisited { get; set; } = 0;

        [Column("comics_viewed")]
        public int ComicsViewed { get; set; } = 0;

        [Column("chapters_read")]
        public int ChaptersRead { get; set; } = 0;

        [Column("ip_address")]
        public string? IpAddress { get; set; }

        [Column("user_agent")]
        public string? UserAgent { get; set; }

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
