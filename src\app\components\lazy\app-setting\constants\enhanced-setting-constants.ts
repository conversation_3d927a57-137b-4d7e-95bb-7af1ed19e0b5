import { InputType } from '@schema';
import { EnhancedSettingOption, SettingCategory } from '../interfaces/setting-interfaces';

// Appearance Settings
export const THEME_SETTING: EnhancedSettingOption = {
  id: 'theme',
  inputType: InputType.Selection,
  name: '<PERSON><PERSON> đề',
  description: '<PERSON>ọn giao diện sáng hoặc tối cho website',
  value: 'light',
  defaultValue: 'light',
  category: SettingCategory.APPEARANCE,
  order: 1,
  icon: 'theme',
  options: [
    { label: 'Sáng', value: 'light' },
    { label: 'Tối', value: 'dark' },
    { label: 'Tự động', value: 'auto' }
  ],
  preview: true
};
export const CARD_COMIC_SIZE_SETTING: EnhancedSettingOption = {
  id: 'cardComicSize',
  inputType: InputType.Selection,
  name: 'Kích thước thẻ truyện',
  description: 'Chọn kích thước của thẻ truyện',
  value: 'medium',
  defaultValue: 'medium',
  category: SettingCategory.APPEARANCE,
  order: 4,
  options: [
    { label: 'Nhỏ', value: 'small' },
    { label: 'Vừa', value: 'medium' },
  ],
  preview: true
};
export const CARD_CHAPTER_SIZE_SETTING: EnhancedSettingOption = {
  id: 'cardChapterSize',
  inputType: InputType.Selection,
  name: 'Số lượng thẻ chương',
  description: 'Chọn kích thước thẻ chương',
  value: 'nhieu',
  defaultValue: 'nhieu',
  category: SettingCategory.APPEARANCE,
  order: 5,
  options: [
    { label: 'Nhiều', value: 'nhieu' },
    { label: 'Ít', value: 'it' },
  ],
  preview: true
};

export const PRIMARY_COLOR_SETTING: EnhancedSettingOption = {
  id: 'primaryColor',
  inputType: InputType.Color,
  name: 'Màu chủ đạo',
  description: 'Màu chính của giao diện website',
  value: '#E83A3A',
  defaultValue: '#E83A3A',
  category: SettingCategory.APPEARANCE,
  order: 2,
  icon: 'palette',
  options: [
    { label: 'Đỏ truyền thống', value: '#E83A3A' },
    { label: 'Xanh dương', value: '#3B82F6' },
    { label: 'Xanh lá', value: '#10B981' },
    { label: 'Tím', value: '#8B5CF6' },
    { label: 'Cam', value: '#F59E0B' }
  ],
  preview: true
};

// export const BACKGROUND_COLOR_SETTING: EnhancedSettingOption = {
//   id: 'backgroundColor',
// 
//   inputType: InputType.Color,
//   name: 'Màu nền',
//   description: 'Màu nền chính của website',
//   value: '#FFFFFF',
//   defaultValue: '#FFFFFF',
//   category: SettingCategory.APPEARANCE,
// 
//   order: 3,
//   icon: 'background',
//   options: [
//     { label: 'Trắng', value: '#FFFFFF' },
//     { label: 'Kem', value: '#FFF8E1' },
//     { label: 'Xám nhạt', value: '#F8F9FA' }
//   ],
//   preview: true
// };

export const FONT_FAMILY_SETTING: EnhancedSettingOption = {
  id: 'fontFamily',
  inputType: InputType.Selection,
  name: 'Font chữ',
  description: 'Lựa chọn font chữ hiển thị',
  value: "'Be Vietnam Pro', sans-serif",
  defaultValue: "'Be Vietnam Pro', sans-serif",
  category: SettingCategory.APPEARANCE,
  order: 4,
  icon: 'font',
  options: [
    { label: 'Be Vietnam Pro', value: "'Be Vietnam Pro', sans-serif" },
    { label: 'Arial', value: "'Arial', sans-serif" },
    { label: 'Times New Roman', value: "'Times New Roman', serif" },
    { label: 'Roboto', value: "'Roboto', sans-serif" },
    { label: 'Inter', value: "'Inter', sans-serif" }
  ],
  preview: true
};

// export const FONT_SIZE_SETTING: EnhancedSettingOption = {
//   id: 'fontSize',
// 
//   inputType: InputType.Range,
//   name: 'Kích thước chữ',
//   description: 'Điều chỉnh kích thước chữ hiển thị',
//   value: 16,
//   defaultValue: 16,
//   category: SettingCategory.APPEARANCE,
// 
//   order: 5,
//   icon: 'text-size',
//   min: 12,
//   max: 24,
//   step: 1,
//   unit: 'px',
//   preview: true
// };

// export const LINE_HEIGHT_SETTING: EnhancedSettingOption = {
//   id: 'lineHeight',
// 
//   inputType: InputType.Selection,
//   name: 'Giãn dòng',
//   description: 'Điều chỉnh khoảng cách giữa các dòng',
//   value: 1.5,
//   defaultValue: 1.5,
//   category: SettingCategory.APPEARANCE,
// 
//   order: 6,
//   icon: 'line-height',
//   options: [
//     { label: 'Sát nhau (1.0)', value: 1.0 },
//     { label: 'Gần nhau (1.2)', value: 1.2 },
//     { label: 'Bình thường (1.5)', value: 1.5 },
//     { label: 'Rộng (1.8)', value: 1.8 },
//     { label: 'Rất rộng (2.0)', value: 2.0 }
//   ],
//   preview: true
// };

// Enhanced Reading Settings for Comic Website
export const READING_MODE_SETTING: EnhancedSettingOption = {
  id: 'readingMode', // Using existing type temporarily
  inputType: InputType.Selection,
  name: 'Chế độ đọc',
  // description: 'Chọn cách hiển thị trang truyện',
  value: 'single',
  defaultValue: 'single',
  category: SettingCategory.READING,
  order: 1,
  icon: 'book-open',
  options: [
    { label: 'Từng trang', value: 'single' },
    { label: 'Cuộn dọc', value: 'vertical' },
    { label: 'Hai trang', value: 'double' },
    { label: 'Webtoon', value: 'webtoon' }
  ]
};

export const SCROLL_SPEED_SETTING: EnhancedSettingOption = {
  id: 'scrollSpeed',
  inputType: InputType.Range,
  name: 'Tốc độ cuộn',
  description: 'Tùy chỉnh tốc độ cuộn khi đọc truyện',
  value: 3,
  defaultValue: 3,
  category: SettingCategory.READING,
  order: 2,
  icon: 'scroll',
  min: 1,
  max: 10,
  step: 1,
  unit: 'x'
};
export const DOUBLE_CLICK_TO_FULLSCREEN_SETTING: EnhancedSettingOption = {
  id: 'DoubleClick',
  inputType: InputType.Toggle,
  name: ' Nhấp 2 lần để toàn màn hình',
  description: 'Nhấp 2 lần để toàn màn hình khi đọc truyện',
  value: false,
  defaultValue: false,
  category: SettingCategory.BEHAVIOR,
  order: 3,
  icon: 'double-click'
};

export const ZOOM_SETTING: EnhancedSettingOption = {
  id: 'zoom-reading',
  inputType: InputType.Range,
  name: 'Tỷ lệ ảnh',
  description: 'Tùy chỉnh kích thước ảnh khi đọc truyện',
  value: 100,
  defaultValue: 100,
  category: SettingCategory.READING,
  order: 2,
  icon: 'scroll',
  min: 50,
  max: 150,
  step: 10,
  unit: '%'
};

export const NIGHT_MODE_SETTING: EnhancedSettingOption = {
  id: 'nightMode', // Using existing type temporarily
  inputType: InputType.Toggle,
  name: 'Chế độ ban đêm',
  // description: 'Bật chế độ ban đêm để đọc truyện',
  value: false,
  defaultValue: false,
  category: SettingCategory.READING,
  order: 3,

};

export const AUTO_NEXT_CHAPTER_SETTING: EnhancedSettingOption = {
  id: 'autoNextChapter',
  inputType: InputType.Toggle,
  name: 'Tự động chuyển chương',
  description: 'Tự động chuyển chương khi ở cuối trang',
  value: false,
  defaultValue: false,
  category: SettingCategory.READING,
  order: 4,
  icon: 'auto-next'
};

// export const PAGE_TRANSITION_SETTING: EnhancedSettingOption = {
//   id: 'pageTransition', // Using existing type temporarily
//   inputType: InputType.Selection,
//   name: 'Hiệu ứng chuyển trang',
//   description: 'Chọn hiệu ứng khi chuyển trang truyện',
//   value: 'slide',
//   defaultValue: 'slide',
//   category: SettingCategory.READING,
//   order: 5,
//   icon: 'transition',
//   options: [
//     { label: 'Không có', value: 'none' },
//     { label: 'Trượt', value: 'slide' },
//     { label: 'Mờ dần', value: 'fade' },
//     { label: 'Zoom', value: 'zoom' }
//   ]
// };

export const PRELOAD_PAGES_SETTING: EnhancedSettingOption = {
  id: 'preloadPages', // Using existing type temporarily
  inputType: InputType.Range,
  name: 'Tải trước trang',
  description: 'Số trang được tải trước để đọc mượt mà hơn',
  value: 3,
  defaultValue: 3,
  category: SettingCategory.READING,
  order: 6,
  icon: 'preload',
  min: 1,
  max: 10,
  step: 1,
  unit: ' trang'
};

export const FIXED_TOOLBAR_SETTING: EnhancedSettingOption = {
  id: 'fixedToolbar',
  inputType: InputType.Toggle,
  name: 'Giữ thanh điều khiển cố định',
  value: false,
  defaultValue: false,
  category: SettingCategory.READING,
  order: 7,
  icon: 'toolbar'
};

export const STYLE_TOOLBAR_SETTING: EnhancedSettingOption = {
  id: 'styleToolbar', // Using existing type temporarily
  inputType: InputType.Selection,
  name: 'Kiểu thanh điều khiển',
  // description: 'Chọn cách hiển thị trang truyện',
  value: 'classic',
  defaultValue: 'classic',
  order: 7,
  category: SettingCategory.READING,
  options: [
    { label: 'Cổ điển', value: 'classic' },
    { label: 'Hiện đại', value: 'modern' },
  ]
};

// Accessibility Settings
export const HIGH_CONTRAST_SETTING: EnhancedSettingOption = {
  id: 'highContrast',
  inputType: InputType.Toggle,
  name: 'Độ tương phản cao',
  description: 'Tăng độ tương phản để dễ đọc hơn',
  value: false,
  defaultValue: false,
  category: SettingCategory.ACCESSIBILITY,
  order: 1,
  icon: 'contrast'
};

export const LARGE_TEXT_SETTING: EnhancedSettingOption = {
  id: 'largeText',
  inputType: InputType.Toggle,
  name: 'Chữ lớn',
  description: 'Sử dụng kích thước chữ lớn hơn',
  value: false,
  defaultValue: false,
  category: SettingCategory.ACCESSIBILITY,
  order: 2,
  icon: 'text-size'
};

export const REDUCE_MOTION_SETTING: EnhancedSettingOption = {
  id: 'reduceMotion',
  inputType: InputType.Toggle,
  name: 'Giảm hiệu ứng động',
  description: 'Giảm hoặc tắt các hiệu ứng chuyển động',
  value: false,
  defaultValue: false,
  category: SettingCategory.ACCESSIBILITY,
  order: 3,
  icon: 'motion'
};

export const KEYBOARD_NAVIGATION_SETTING: EnhancedSettingOption = {
  id: 'keyboardNavigation',
  inputType: InputType.Toggle,
  name: 'Điều hướng bàn phím',
  description: 'Bật hỗ trợ điều hướng bằng bàn phím',
  value: true,
  defaultValue: true,
  category: SettingCategory.ACCESSIBILITY,
  order: 4,
  icon: 'keyboard'
};

export const SCREEN_READER_SETTING: EnhancedSettingOption = {
  id: 'screenReader',
  inputType: InputType.Toggle,
  name: 'Hỗ trợ đọc màn hình',
  description: 'Tối ưu hóa cho phần mềm đọc màn hình',
  value: false,
  defaultValue: false,
  category: SettingCategory.ACCESSIBILITY,
  order: 5,
  icon: 'screen-reader'
};

// // Advanced Settings
// export const DEVELOPER_MODE_SETTING: EnhancedSettingOption = {
//   id: 'developerMode',
// 
//   inputType: InputType.Toggle,
//   name: 'Chế độ nhà phát triển',
//   description: 'Bật các tính năng dành cho nhà phát triển',
//   value: false,
//   defaultValue: false,
//   category: SettingCategory.ADVANCED,
// 
//   order: 1,
//   icon: 'code'
// };

// export const DEBUG_MODE_SETTING: EnhancedSettingOption = {
//   id: 'debugMode',
// 
//   inputType: InputType.Toggle,
//   name: 'Chế độ debug',
//   description: 'Hiển thị thông tin debug trong console',
//   value: false,
//   defaultValue: false,
//   category: SettingCategory.ADVANCED,
// 
//   order: 2,
//   icon: 'bug'
// };

// export const CACHE_MANAGEMENT_SETTING: EnhancedSettingOption = {
//   id: 'cacheManagement',
// 
//   inputType: InputType.Selection,
//   name: 'Quản lý cache',
//   description: 'Cài đặt quản lý bộ nhớ đệm',
//   value: 'auto',
//   defaultValue: 'auto',
//   category: SettingCategory.ADVANCED,
// 
//   order: 3,
//   icon: 'database',
//   options: [
//     { label: 'Tự động', value: 'auto' },
//     { label: 'Tích cực', value: 'aggressive' },
//     { label: 'Tiết kiệm', value: 'conservative' },
//     { label: 'Tắt', value: 'disabled' }
//   ]
// };

// export const EXPERIMENTAL_FEATURES_SETTING: EnhancedSettingOption = {
//   id: 'experimentalFeatures',
// 
//   inputType: InputType.Toggle,
//   name: 'Tính năng thử nghiệm',
//   description: 'Bật các tính năng đang trong giai đoạn thử nghiệm',
//   value: false,
//   defaultValue: false,
//   category: SettingCategory.ADVANCED,
// 
//   order: 4,
//   icon: 'flask'
// };

// export const DATA_USAGE_SETTING: EnhancedSettingOption = {
//   id: 'dataUsage',
// 
//   inputType: InputType.Selection,
//   name: 'Sử dụng dữ liệu',
//   description: 'Cài đặt mức độ sử dụng dữ liệu',
//   value: 'normal',
//   defaultValue: 'normal',
//   category: SettingCategory.ADVANCED,
// 
//   order: 5,
//   icon: 'wifi',
//   options: [
//     { label: 'Tiết kiệm', value: 'low' },
//     { label: 'Bình thường', value: 'normal' },
//     { label: 'Cao', value: 'high' },
//     { label: 'Không giới hạn', value: 'unlimited' }
//   ]
// };

// Language & Localization
export const LANGUAGE_SETTING: EnhancedSettingOption = {
  id: 'language',
  inputType: InputType.Selection,
  name: 'Ngôn ngữ',
  description: 'Ngôn ngữ hiển thị cho website',
  value: 'vi-VN',
  defaultValue: 'vi-VN',
  category: SettingCategory.APPEARANCE,
  order: 7,
  icon: 'language',
  options: [
    { label: 'Tiếng Việt', value: 'vi-VN' },
  ]
};

// All Enhanced Settings Array
export const ENHANCED_SETTINGS: EnhancedSettingOption[] = [
  // Appearance Settings
  THEME_SETTING,
  // PRIMARY_COLOR_SETTING,
  // BACKGROUND_COLOR_SETTING,
  // FONT_FAMILY_SETTING,
  // FONT_SIZE_SETTING,
  // LINE_HEIGHT_SETTING,
  LANGUAGE_SETTING,
  CARD_COMIC_SIZE_SETTING,
  CARD_CHAPTER_SIZE_SETTING,

  // Reading Settings
  READING_MODE_SETTING,
  // SCROLL_SPEED_SETTING,
  // IMAGE_QUALITY_SETTING,
  NIGHT_MODE_SETTING,
  AUTO_NEXT_CHAPTER_SETTING,
  // PAGE_TRANSITION_SETTING,
  PRELOAD_PAGES_SETTING,
  STYLE_TOOLBAR_SETTING,
  FIXED_TOOLBAR_SETTING,
  ZOOM_SETTING,
  //
  DOUBLE_CLICK_TO_FULLSCREEN_SETTING,

  // Accessibility Settings
  HIGH_CONTRAST_SETTING,
  LARGE_TEXT_SETTING,
  REDUCE_MOTION_SETTING,
  KEYBOARD_NAVIGATION_SETTING,
  SCREEN_READER_SETTING,

  // Advanced Settings
  // DEVELOPER_MODE_SETTING,
  // DEBUG_MODE_SETTING,
  // CACHE_MANAGEMENT_SETTING,
  // EXPERIMENTAL_FEATURES_SETTING,
  // DATA_USAGE_SETTING
];
