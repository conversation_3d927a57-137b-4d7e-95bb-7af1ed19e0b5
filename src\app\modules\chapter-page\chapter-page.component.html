<div class="chapter-container">
  <div class="header-container">
    <app-breadcrumb
      class="breadcrumb-wrapper"
      [Links]="[
        { label: 'Trang chủ', url: '/' },
        {
          label: comic.title,
          url: '/truyen-tranh/' + comic.url + '-' + comic.id
        },
        { label: mainChapter.title, url: '' }
      ]"
    >
    </app-breadcrumb>
    <app-anouncement></app-anouncement>
  </div>
</div>
<div
  #screenContainer
  (scroll)="handleFullScreenScroll($event)"
  class="chapter-container scrollbar-style-1"
>
  <div class="main-container">
    <!-- Chapter Header -->
    <section #HeaderContainer class="chapter-header-container">
      <div class="chapter-header-card">
        <!-- Chapter Info -->
        <div class="chapter-info-section">
          <!-- Report Error Button -->

          <!-- Comic Title -->
          <div class="comic-title-section">
            <h1 class="comic-title">
              <a
                [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
                [title]="comic.title"
                class="comic-title-link"
              >
                {{ comic.title }}
              </a>
            </h1>
          </div>

          <!-- Chapter Details -->
          <div class="chapter-details">
            <h2 class="chapter-title">{{ mainChapter.title }}</h2>
            <time class="chapter-date" [dateTime]="mainChapter.updateAt | date : 'yyyy-MM-dd'">
              Đăng lúc: {{ mainChapter.updateAt | date : 'dd/MM/yyyy' }}
            </time>
          </div>
        </div>

        <!-- Server Selection -->
        <div class="server-selection-section">
          <div class="server-list">
            <button
              *ngFor="
                let serverId of showAllServers
                  ? listChapterServerIds
                  : (listChapterServerIds ?? []).slice(0, 3);
                let i = index
              "
              (click)="changeServer(serverId, i)"
              class="server-button"
              [class.server-button-active]="i === selectedServerId"
            >
              <svg class="server-icon" viewBox="0 0 24 24">
                <path d="M7 18a4.6 4.4 0 0 1 0 -9h0a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12" />
              </svg>
              <span class="server-text">Server {{ i + 1 }}</span>
            </button>

            <button
              (click)="showMoreServer()"
              *ngIf="listChapterServerIds && listChapterServerIds.length > 3"
              class="server-expand-button"
            >
              <svg
                class="expand-icon"
                [class.expand-icon-rotated]="showAllServers"
                viewBox="0 0 24 24"
              >
                <path d="M18 15l-6-6l-6 6h12" />
              </svg>
            </button>
            <button class="report-error-button" (click)="reportError()">
              <svg class="report-icon" viewBox="0 0 24 24">
                <path
                  d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
                />
                <line x1="12" y1="9" x2="12" y2="13" />
                <line x1="12" y1="17" x2="12.01" y2="17" />
              </svg>
              <span class="report-text">Báo lỗi</span>
            </button>
          </div>
        </div>
      </div>

      <nav #controlBarContainer class="w-full h-12">
        <div #controlBar class="control-bar" [ngClass]="chapterSetting.toolbarStyle">
          <!-- Home Button -->
          <div class="control-group">
            <a href="" title="Trang chủ" class="control-button control-button-home">
              <svg class="control-icon" viewBox="0 0 24 24">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                <polyline points="9,22 9,12 15,12 15,22" />
              </svg>
            </a>
          </div>

          <!-- Fullscreen Button -->
          <div class="control-group">
            <button title="Toàn màn hình" class="control-button" (click)="toggleFullscreen()">
              <svg class="control-icon" viewBox="0 0 24 24">
                <path
                  d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"
                />
              </svg>
            </button>
          </div>

          <!-- Chapter Navigation -->
          <div class="chapter-navigation-group">
            <button
              class="nav-button nav-button-prev"
              (click)="navigateChapter(true)"
              aria-label="Chương trước"
              [disabled]="isImageLoading"
              [class.nav-button-active]="
                this.comic.chapters!.length > 0 &&
                mainChapter.slug !== this.comic.chapters![this.comic.chapters!.length - 1].slug
              "
            >
              <svg class="nav-icon" viewBox="0 0 24 24">
                <polyline points="15 18 9 12 15 6" />
              </svg>
            </button>

            <div class="chapter-selector-wrapper">
              <app-chapter-selector
                [chapters]="this.comic.chapters!"
                [mainChapter]="mainChapter"
                (chapterChange)="onChangeChapter($event)"
                [topToBottom]="true"
              >
              </app-chapter-selector>
            </div>

            <button
              class="nav-button nav-button-next"
              (click)="navigateChapter(false)"
              aria-label="Chương tiếp"
              [disabled]="isImageLoading"
              [class.nav-button-active]="
                this.comic.chapters!.length > 0 && mainChapter.slug !== this.comic.chapters![0].slug
              "
            >
              <svg class="nav-icon" viewBox="0 0 24 24">
                <polyline points="9 18 15 12 9 6" />
              </svg>
            </button>
          </div>

          <!-- Zoom Controls -->
          <div
            class="control-group zoom-group"
            (appClickOutside)="zoomPanel.classList.remove('zoom-panel-active')"
          >
            <button
              title="Thu phóng"
              class="control-button zoom-button"
              (click)="zoomImage(!zoomData.isZoomIn); zoomPanel.classList.add('zoom-panel-active')"
            >
              <svg class="control-icon" viewBox="0 0 24 24">
                @if (!zoomData.isZoomIn) {
                <circle cx="11" cy="11" r="8" />
                <line x1="21" y1="21" x2="16.65" y2="16.65" />
                <line x1="11" y1="8" x2="11" y2="14" />
                <line x1="8" y1="11" x2="14" y2="11" />
                } @else {
                <circle cx="11" cy="11" r="8" />
                <line x1="21" y1="21" x2="16.65" y2="16.65" />
                <line x1="8" y1="11" x2="14" y2="11" />
                }
              </svg>
            </button>
            <div #zoomPanel class="zoom-panel">
              <div class="zoom-info">
                <span class="zoom-percentage">{{ getZoomPercentage() }}%</span>
                <div class="zoom-controls">
                  <button class="zoom-control-btn" (click)="zoomImage(false)" title="Thu nhỏ">
                    <svg class="zoom-control-icon" viewBox="0 0 24 24">
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                  </button>
                  <button class="zoom-control-btn" (click)="zoomImage(true)" title="Phóng to">
                    <svg class="zoom-control-icon" viewBox="0 0 24 24">
                      <line x1="12" y1="5" x2="12" y2="19" />
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                  </button>
                </div>
              </div>

              <button (click)="resetView()" title="Đặt lại" class="zoom-reset-btn">
                <svg class="zoom-reset-icon" viewBox="0 0 24 24">
                  <path
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            </div>

            <!-- Zoom Panel -->
          </div>

          <!-- Settings Button -->
          <div class="control-group">
            <button (click)="openSetting()" title="Cài đặt" class="control-button settings-button">
              <svg
                class="control-icon"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" />
                <path
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </button>
          </div>
        </div>
      </nav>

      <!-- Control Bar with Enhanced Animations -->
    </section>

    <!-- Banners -->

    <!-- Reading Container -->
    <div (dblclick)="toggleFullscreen()" class="reading-container" #readingContainer>
      @if(isBrowser && !isImageLoading) {
      <!-- <div class="banner-section">
        <app-banner2 [codes]="['2051269', '2051272']"></app-banner2>
        <app-banner3></app-banner3>
      </div> -->
      }
      <div
        #imageContainer
        [style.width]="getImageWidth()"
        [style.left]="getImageLeft()"
        id="image-container"
        class="reading-content"
      >
        <!-- Scroll Navigation -->
        <div
          class="scroll-navigation"
          [class.scroll-navigation-hidden]="
            chapterSetting.isVertical || chapterSetting.isFullScreen
          "
        >
          <button
            (click)="scrollHorizontal(-1)"
            class="scroll-btn scroll-btn-prev"
            title="Trang trước"
          >
            <svg class="scroll-btn-icon" viewBox="0 0 24 24">
              <polyline points="15 18 9 12 15 6" />
            </svg>
            <span class="scroll-btn-text">Trước</span>
          </button>

          <button
            (click)="scrollHorizontal(1)"
            class="scroll-btn scroll-btn-next"
            title="Trang tiếp"
          >
            <span class="scroll-btn-text">Tiếp</span>
            <svg class="scroll-btn-icon" viewBox="0 0 24 24">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
        </div>

        <!-- Loading State -->
        <div *ngIf="isImageLoading" class="loading-container">
          <div class="loading-content">
            <div class="loading-spinner">
              <svg class="loading-icon" viewBox="0 0 24 24">
                <circle class="loading-circle-bg" cx="12" cy="12" r="10" />
                <circle class="loading-circle-progress" cx="12" cy="12" r="10" />
              </svg>
            </div>
            <div class="loading-text">
              <h3 class="loading-title">Đang tải chương...</h3>
              <p class="loading-subtitle">Vui lòng đợi trong giây lát</p>
            </div>
          </div>

          <!-- Loading Skeleton -->
        </div>

        <!-- Chapter Images -->
        <ng-container *ngIf="isBrowser && !isImageLoading">
          <div
            *ngFor="let page of listImgs; let i = index; trackBy: trackByPageId"
            class="chapter-page-container"
          >
            <img
              #imageElement
              [loading]="i <= this.chapterSetting.preloadPages ? 'eager' : 'lazy'"
              class="chapter-page-image"
              [alt]="comic.url + 'chapter-page-' + (i + 1)"
              [ngClass]="{
                'chapter-page-horizontal': !chapterSetting.isVertical,
                'night-mode': chapterSetting.isNightMode
              }"
              [src]="page.url"
              (error)="onError($event)"
              (load)="onLoad($event)"
            />
            <!-- Tạm giữ chỗ -->
            <div
              *ngIf="!(imageElement.complete || imageElement.naturalWidth > 0)"
              class="skeleton"
              style="aspect-ratio: 2/3"
            ></div>
          </div>
        </ng-container>
      </div>
      @if(isBrowser && !isImageLoading) {
      <!-- <div class="banner-section">
        <app-banner2 [codes]="['2052076', '2052074']"></app-banner2>
      </div> -->
      }
      <div #EndChapter class="end-chapter-navigation">
        <div class="end-chapter-content">
          <button
            class="end-nav-button end-nav-prev"
            (click)="navigateChapter(true)"
            aria-label="Chương trước"
            [disabled]="isImageLoading"
            [class.end-nav-active]="
              this.comic.chapters!.length > 0 &&
              mainChapter.slug !== this.comic.chapters![this.comic.chapters!.length - 1].slug
            "
          >
            <svg class="end-nav-icon" viewBox="0 0 24 24">
              <polyline points="15 18 9 12 15 6" />
            </svg>
            <span class="end-nav-text">Chương trước</span>
          </button>

          <div class="end-chapter-info">
            <h3 class="end-chapter-title">Kết thúc chương</h3>
            <p class="end-chapter-subtitle">{{ mainChapter.title }}</p>
          </div>

          <button
            class="end-nav-button end-nav-next"
            (click)="navigateChapter(false)"
            aria-label="Chương tiếp theo"
            [disabled]="isImageLoading"
            [class.end-nav-active]="
              this.comic.chapters!.length > 0 && mainChapter.slug !== this.comic.chapters![0].slug
            "
          >
            <span class="end-nav-text">Chương tiếp</span>
            <svg class="end-nav-icon" viewBox="0 0 24 24">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
        </div>
      </div>
      @if (isBrowser) {
      <app-comment [comicId]="comic.id" [chapterID]="mainChapter.id"></app-comment>
      }
    </div>
    <!-- End Chapter Navigation -->

    <!-- Scroll to Top Button -->
    <button
      (click)="scrollToTop($event)"
      class="scroll-to-top-btn"
      [class.scroll-to-top-visible]="showScrollToTop"
      title="Lên đầu trang"
      type="button"
    >
      <svg class="scroll-to-top-icon" viewBox="0 0 24 24">
        <polyline points="18 15 12 9 6 15" />
      </svg>
    </button>
  </div>
</div>
