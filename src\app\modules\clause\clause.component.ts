import { Component, OnInit } from '@angular/core';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
    selector: 'app-clause',
    templateUrl: './clause.component.html',
    styleUrl: './clause.component.scss',
    standalone: false
})
export class ClauseComponent implements OnInit {
    host = '';
    siteName = 'MeTruyenMoi';
    currentDate = '';

    constructor(
        private urlService: UrlService,
        private seoService: SeoService,
    ) {
        this.host = this.urlService.baseUrl;
        this.currentDate = new Date().toLocaleDateString('vi-VN');
    }

    ngOnInit(): void {
        this.setupSEO();
    }

    private setupSEO(): void {
        this.seoService.setTitle('Điều khoản sử dụng - Quy định và chính sách dịch vụ');
        this.seoService.addTags([
            { name: 'description', content: 'Điều khoản sử dụng chi tiết của MeTruyenMoi - Quy định về quyền và nghĩa vụ của người dùng khi sử dụng dịch vụ đọc truyện tranh trực tuyến.' },
            { name: 'robots', content: 'index, follow' },
            { name: 'author', content: 'MeTruyenMoi' },
            { property: 'og:description', content: 'Điều khoản sử dụng chi tiết - Quy định về quyền và nghĩa vụ của người dùng MeTruyenMoi' },
            { property: 'og:title', content: 'Điều khoản sử dụng - MeTruyenMoi' },
            { property: 'og:url', content: `${this.host}/dieu-khoan` },
            { property: 'og:type', content: 'website' },
            { property: 'og:site_name', content: 'MeTruyenMoi' },
            { property: 'og:locale', content: 'vi_VN' },
            { itemprop: 'name', content: 'Điều khoản sử dụng' },
            { itemprop: 'description', content: 'Điều khoản sử dụng chi tiết của MeTruyenMoi' },
            { name: 'twitter:card', content: 'summary' },
            { name: 'twitter:title', content: 'Điều khoản sử dụng - MeTruyenMoi' },
            { name: 'twitter:description', content: 'Điều khoản sử dụng chi tiết - Quy định về quyền và nghĩa vụ của người dùng' }
        ]);
        this.seoService.updateLink('canonical', `${this.host}/dieu-khoan`);
    }

    // Scroll to section method for table of contents
    scrollToSection(sectionId: string): void {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
}
