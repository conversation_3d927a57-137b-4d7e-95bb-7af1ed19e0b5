using ComicAPI.DTOs;
using ComicAPI.Enums;
using ComicAPI.Models.Activity;

namespace ComicAPI.Reposibility
{
    public interface IUserActivityRepository
    {
        // Activity CRUD Operations
        Task<int> CreateActivityAsync(UserActivity activity);
        Task<List<int>> CreateActivitiesBatchAsync(IEnumerable<UserActivity> activities);
        Task<UserActivity?> GetActivityByIdAsync(int id);
        Task<List<UserActivity>> GetUserActivitiesAsync(int userId, int page = 1, int pageSize = 20, 
            UserActivityType? activityType = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetUserActivitiesCountAsync(int userId, UserActivityType? activityType = null, 
            DateTime? fromDate = null, DateTime? toDate = null);

        // Reading History CRUD Operations
        Task<int> CreateReadingHistoryAsync(UserReadingHistory history);
        Task<List<int>> CreateReadingHistoriesBatchAsync(IEnumerable<UserReadingHistory> histories);
        Task<UserReadingHistory?> GetReadingHistoryAsync(int userId, int comicId, int chapterId);
        Task<bool> UpdateReadingHistoryAsync(UserReadingHistory history);
        Task<List<UserReadingHistory>> GetUserReadingHistoryAsync(int userId, int page = 1, int pageSize = 20, 
            int? comicId = null, bool? isCompleted = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetUserReadingHistoryCountAsync(int userId, int? comicId = null, bool? isCompleted = null);

        // Login History CRUD Operations
        Task<int> CreateLoginHistoryAsync(UserLoginHistory loginHistory);
        Task<List<UserLoginHistory>> GetUserLoginHistoryAsync(int userId, int page = 1, int pageSize = 20, 
            DateTime? fromDate = null, DateTime? toDate = null);
        Task<bool> UpdateLoginHistoryLogoutAsync(int loginHistoryId, DateTime logoutTime, int sessionDurationSeconds);

        // Session Activity CRUD Operations
        Task<int> CreateSessionActivityAsync(UserSessionActivity sessionActivity);
        Task<UserSessionActivity?> GetActiveSessionAsync(int userId, string sessionId);
        Task<bool> UpdateSessionActivityAsync(UserSessionActivity sessionActivity);
        Task<bool> EndSessionAsync(string sessionId, DateTime endTime);
        Task<List<UserSessionActivity>> GetUserActiveSessionsAsync(int userId);
        Task<int> GetUserActiveSessionsCountAsync(int userId);

        // Analytics and Statistics
        Task<UserActivityStatsDTO> GetUserActivityStatsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<List<DailyActivityDTO>> GetDailyActivityAsync(int userId, DateTime fromDate, DateTime toDate);
        Task<List<MonthlyActivityDTO>> GetMonthlyActivityAsync(int userId, int year);
        Task<List<GenreStatsDTO>> GetUserGenreStatsAsync(int userId);
        Task<Dictionary<UserActivityType, int>> GetActivityTypeCountsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);

        // Performance and Utility Methods
        Task<DateTime?> GetUserLastActivityAsync(int userId);
        Task<bool> UpdateUserLastActivityAsync(int userId, DateTime lastActivity);
        Task<bool> IsUserActiveAsync(int userId, TimeSpan timeWindow);
        Task<List<int>> GetActiveUsersAsync(TimeSpan timeWindow, int limit = 100);
        Task<Dictionary<int, DateTime>> GetUsersLastActivityBatchAsync(IEnumerable<int> userIds);

        // Data Management
        Task<int> CleanupOldActivitiesAsync(DateTime cutoffDate);
        Task<int> CleanupOldLoginHistoriesAsync(DateTime cutoffDate);
        Task<int> CleanupInactiveSessionsAsync(DateTime cutoffDate);
        Task<bool> ArchiveOldDataAsync(DateTime cutoffDate);

        // Bulk Operations for Performance
        Task<bool> BulkInsertActivitiesAsync(IEnumerable<UserActivity> activities);
        Task<bool> BulkInsertReadingHistoriesAsync(IEnumerable<UserReadingHistory> histories);
        Task<bool> BulkUpdateReadingProgressAsync(IEnumerable<(int userId, int comicId, int chapterId, float progress, int readingTime)> updates);

        // Cache Management
        Task InvalidateUserCacheAsync(int userId);
        Task<T?> GetFromCacheAsync<T>(string key) where T : class;
        Task SetCacheAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task RemoveFromCacheAsync(string key);

        // Advanced Queries
        Task<List<UserActivity>> GetRecentActivitiesByTypeAsync(UserActivityType activityType, int limit = 100, DateTime? since = null);
        Task<Dictionary<int, int>> GetTopActiveUsersAsync(DateTime fromDate, DateTime toDate, int limit = 50);
        Task<List<(int ComicId, int ReadCount)>> GetMostReadComicsAsync(int userId, int limit = 10);
        Task<List<(int ChapterId, DateTime LastRead)>> GetRecentlyReadChaptersAsync(int userId, int limit = 20);
        Task<Dictionary<DateTime, int>> GetUserActivityHeatmapAsync(int userId, DateTime fromDate, DateTime toDate);
    }
}
